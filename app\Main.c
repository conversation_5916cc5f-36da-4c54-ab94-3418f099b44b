/*==============================================================================
            Copyright(C) 2021-2029.  XXXXXX Tech. Co., Ltd.
--------------------------------------------------------------------------------
Project No.   : XXXXXX
File Name     : MAIN..C	
Description   : 
              ；    
              ；   
Notice        :
             1: 
             2:

Author        :  
Start Date    :  
Release Date  :  
Approve Date  :
Version       : V1.0
CheckSum      : XXXXXXX
Function List :
             1:
             2:

RevisionHistory:
Rev#  CheckSum   Date     Author     Comments(Function+Date)
-----+--------+----------+---------+--------------------------------------------
0.0 2021/10/08 Author Just build the function
0.1
==============================================================================*/
 

//-------------------------------------------------------------------------------
#ifndef  MAIN_C
	#define  MAIN_C
#endif
//-------------------------------------------------------------------------------

//---------------


#include	".\head\Config.h"




/****************************************************************************
 * Function Description:main process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void main(void)
{     
		EA = 0;			       		 				  //disable all interrupting
		InitSys(); 											//initial system SFR registering
		DelayNms(200);								  //delay 200ms	
    InitPort();											//initial port 
    InitKeyScanRam();								//initial key scaning user ram
    InitKeyProcRam();								//initial key processing user ram
		InitInterruptRam();							//initial interrupting user ram
		InitAllLedOff();								//initial all led off user ram
		InitAllLedRamApp();							//initial all led application user ram
    InitT0();												//T0 initial is 1ms
    InitT1(); 											//T1 initial is 5mS
	  InitINT1();											//initial exter interrupting number 1
	  InitINT2();		  								//initial exter interrupting number 2
    InitPwm();											//initialPWM
		Uart0Init();  								  //initialUART
		Timer1_Init();									//initial Timer1 for PWM detection
	  IEN0  = Bin(10010010);	  			//bit7:开总中断_EA;使能;bit4：UART0,bit1:定时0_ET0;
	  IEN1 |= Bin(00100000);	  			//bit5:EPWM
		EA = 1;												  //set all interrupting enable
//while(1)
//{
//	LED6 ^= 1;	
//	if(LED6 == 0)
//	{
//		DelayNms(100);
//		SBUF = 0x55;
//	}
//	else DelayNms(1000);	
//}	
		while(1)
		{
				MonitorCpuTimer();					//cls WDT 2048ms
				InitCpuACSet();							//set port,adc,pwm
				KeyScan();									//key scan process
				KeyProc();			  					//key function process
				LedApp();										//LED display process
			  CommFunProc();							//uant commontin process
				LedApp();										//经过接收数据帧后更新LED显示

				// PWM占空比检测和LED控制逻辑
				duty_shadow = duty;  // 拷贝一次避免冲突

				// 只控制P1.0, P1.1, P1.4, P1.5 (避免影响UART P1.2/P1.3和LED P1.6/P1.7)
				// 清除要控制的位，保持其他位不变
				P1 &= 0xCC;  // 保持P1.2, P1.3, P1.6, P1.7，清除P1.0, P1.1, P1.4, P1.5

				if (duty_shadow >= 85 && duty_shadow <= 95) {
					P1 |= 0x00;  // 最高档：P1.0, P1.1, P1.4, P1.5全部为0 (LED亮)
				}
				else if (duty_shadow >= 70) {
					P1 |= 0x01;  // P1.0=1, 其他为0
				}
				else if (duty_shadow >= 55) {
					P1 |= 0x03;  // P1.0, P1.1=1, P1.4, P1.5=0
				}
				else if (duty_shadow >= 40) {
					P1 |= 0x13;  // P1.0, P1.1, P1.4=1, P1.5=0
				}
				else if (duty_shadow >= 25) {
					P1 |= 0x33;  // P1.0, P1.1, P1.4, P1.5=1 (所有控制的LED关闭)
				}
				else if (duty_shadow >= 10) {
					P1 |= 0x33;  // 同上
				}
				else {
					P1 |= 0x33;  // 所有控制的LED关闭
				}
		}
}

