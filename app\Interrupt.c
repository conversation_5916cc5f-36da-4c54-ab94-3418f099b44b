/*==============================================================================
			Copyright(C) 2021-2029.  XXXXXX Tech. Co., Ltd.
--------------------------------------------------------------------------------
Project No.   : XXXXXX
File Name     : INTERRUPT..C
Description   :
			  ??
			  ??
Notice        :
			 1:
			 2:

Author        :
Start Date    :
Release Date  :
Approve Date  :
Version       : V1.0
CheckSum      : XXXXXXX
Function List :
			 1:
			 2:

RevisionHistory:
Rev#  CheckSum   Date     Author     Comments(Function+Date)
-----+--------+----------+---------+--------------------------------------------
0.0 2021/10/08 Author Just build the function
0.1
==============================================================================*/

//-------------------------------------------------------------------------------
#ifndef INTERRUPT_C
#define INTERRUPT_C
#endif
//-------------------------------------------------------------------------------

//---------------

#include ".\head\Config.h"
void UART_SendByte(unsigned char dat);

// PWM?????????????
unsigned char xdata duty = 0;           // ???PWM??????????
unsigned char xdata count = 0;
unsigned char xdata high_count = 0;
unsigned char xdata duty_shadow = 0;

/****************************************************************************
 * Function Description:InitInterruptRam process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void InitInterruptRam(void)
{
	TimeFlagStr.Task125usCnt = 0x00;

	TimeFlagStr.Task5msCnt = 0x00;
	TimeFlagStr.Task10msCnt = 0x00;
	TimeFlagStr.Task10msFlag = 0x00;

	TimeFlagStr.Task50msCnt = 0x00;
	TimeFlagStr.Task50msFlag = 0x00;

	TimeFlagStr.Task500msCnt = 0x00;
	TimeFlagStr.Task500msFlag = 0x00;

	TimeFlagStr.Task1000msCnt = 0x00;
	TimeFlagStr.Task1000msFlag = 0x00;
}

/****************************************************************************
 * Function Description:EX0_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void EX0_INT(void) interrupt 0 // ??????0???????:
{
	IE0 = 0; // TCON.1
	EX0 = 0; // IE2=EXF0.0,??????0??????????
}

/****************************************************************************
 * Function Description:Timer0_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void Timer0_INT(void) interrupt 1 ////???????0???????,125US test is ok
{
	TF0 = 0; // TCON.5??????????????
	TR0 = 0;
	TL0 = (unsigned char)((D_T0_125US_CNT >> 0) & 0x00FF); // t0??????
	TH0 = (unsigned char)((D_T0_125US_CNT >> 8) & 0x00FF); // t0???????;D_T0_125US_CNT
	TR0 = 1;

	////test
	// P1_5 = ~P1_5;
	////test

	TimeFlagStr.TaskMonitorFlag = 0x01; // ????125us??????????????

	TimeFlagStr.Task125usCnt++;
	if (TimeFlagStr.Task125usCnt >= 8) // 125us*8=1ms,
	{
		TimeFlagStr.Task125usCnt = 0x00;

		TimeFlagStr.Task5msCnt++;
		if (TimeFlagStr.Task5msCnt >= 5) // 5ms
		{
			TimeFlagStr.Task5msCnt = 0x00;
		}

		TimeFlagStr.Task10msCnt++;
		if (TimeFlagStr.Task10msCnt >= 10) // 10ms
		{
			TimeFlagStr.Task10msCnt = 0x00;
			TimeFlagStr.Task10msFlag = 0x01;

			KeyDrvStr.KeyScanFlag = 0x01;

			TimeFlagStr.Task50msCnt++;
			if (TimeFlagStr.Task50msCnt >= 5) // 10ms*3=30ms
			{
				TimeFlagStr.Task50msCnt = 0x00;
				TimeFlagStr.Task50msFlag = 0x01;

				Uart.Cmd = RURN;
				Uart.TxdResponseFlag = 0x01; // ???????
				Uart.RxdCnt = 0x00;
				Uart.RxdFSM = 0x00;
				REN = 1; // ???????
			}

			// ???????
			TimeFlagStr.Task500msCnt++; // every 10ms interrupt,0.5s need 50 times
			if (TimeFlagStr.Task500msCnt >= 50)
			{
				TimeFlagStr.Task500msCnt = 0x00;
				TimeFlagStr.Task500msFlag = 0x01;

				TimeFlagStr.Task1000msCnt++; // every 500ms interrupt,1s need 2 times
				if (TimeFlagStr.Task1000msCnt >= 2)
				{
					TimeFlagStr.Task1000msCnt = 0x00;
					TimeFlagStr.Task1000msFlag = 0x01;
				}
			}
		}

		//        if(LedWorkGroupStr.Led1FlashMode == 0x01)
		//        {
		//            LedWorkGroupStr.Led1SlowFlashCnt = 0x00;
		//            LedWorkGroupStr.Led1FastFlashCnt++;
		//            if(LedWorkGroupStr.Led1FastFlashCnt >= 16)						//20
		//            {
		//                LedWorkGroupStr.Led1FastFlashCnt = 0x00;
		//            }
		//        }
		//        else if(LedWorkGroupStr.Led1FlashMode == 0x02)
		//        {
		//            LedWorkGroupStr.Led1FastFlashCnt = 0x00;
		//            LedWorkGroupStr.Led1SlowFlashCnt++;
		//            if(LedWorkGroupStr.Led1SlowFlashCnt >= 48)						//50
		//            {
		//                LedWorkGroupStr.Led1SlowFlashCnt = 0x00;
		//            }
		//        }
		//        else
		//        {
		//            LedWorkGroupStr.Led1SlowFlashCnt = 0x00;
		//            LedWorkGroupStr.Led1FastFlashCnt = 0x00;
		//        }
	}
}

/****************************************************************************
 * Function Description:EX1_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void EX1_INT(void) interrupt 2 // ??????1???????
{
	IE1 = 0; // TCON.1
	EX1 = 0; // IE2=EXF0.0,??????2??????????
}

/****************************************************************************
 * Function Description:Timer1_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void Timer1_INT(void) interrupt 3 // ???????1???????:
{
    TF1 = 0;  // ????????

    // ?????????????,?100us????
    TL1 = 0xA0;
    TH1 = 0xFF;

    count++;
    if (PWM_IN) high_count++;

    if (count >= 100) {  // ?????????,?10ms
        // ??????,???????
        if (count > 0) {
            duty = (high_count * 100) / count;
        }

        // ???????
        count = 0;
        high_count = 0;
    }
}


///****************************************************************************
// * Function Description:EUART0_INT process
// * Input parameter     :void
// * Output paramter     :void
// ****************************************************************************/
// void EUART0_INT(void) interrupt 4
//{
//    unsigned char xdata TempSBUF;

//    // ========== ???????? ==========
//    if (RI)
//    {
//				LED1 ^= 1;
//        RI = 0;
//        TempSBUF = SBUF;
//				UART_SendByte(TempSBUF);
//        switch (Uart.RxdFSM)
//        {
//            case 0X00: // ?????1 (0x55)
//                if (TempSBUF == 0x55)
//                {
//                    Uart.RxdData[0] = TempSBUF;
//                    Uart.RxdCnt = 1;
//                    Uart.RxdFSM = 1;
//										UART_SendByte(TempSBUF);
//                }
//                else
//                {
//                    Uart.RxdCnt = 0;
//                    Uart.RxdFSM = 0;
//                }
//								UART_SendByte(TempSBUF); // ??????????
//                break;

//            case 0X01: // ?????2 (0xAA)
//                if (TempSBUF == 0xAA)
//                {
//                    Uart.RxdData[1] = TempSBUF;
//                    Uart.RxdCnt = 2;
//                    Uart.RxdFSM = 2;
//                }
//                else
//                {
//                    Uart.RxdCnt = 0;
//                    Uart.RxdFSM = 0;
//                }
//								UART_SendByte(TempSBUF);
//                break;

//            case 0X02: // ??????????
//                Uart.RxdData[2] = TempSBUF;
//                Uart.RxdCnt = 3;
//                Uart.RxdFSM = 3;
//								UART_SendByte(TempSBUF);
//                break;

//            case 0X03: // ??????????
//                Uart.RxdData[3] = TempSBUF;
//                Uart.RxdCnt = 4;
//                Uart.RxdFSM = 0;
//								Uart.RxdResponseFlag = 0x01;
//								CommDealResponse(); //???????????????????
//								UART_SendByte(TempSBUF);
//								UART_SendByte(Uart.RxdResponseFlag);
//                break;

//            default:
//                Uart.RxdCnt = 0;
//                Uart.RxdFSM = 0;
//                break;
//        }
//    }

//
//	// ========== ???????? ==========
//    if (TI)
//    {
//				LED3 ^= 1;
//        TI = 0;

//        if (Uart.TxdDataCnt < Uart.TxdDataLength)
//        {
//            Uart.TxdDataCnt++;
//            SBUF = Uart.TxdData[Uart.TxdDataCnt];
//        }
//        else
//        {
//            REN = 1; // ???????
//        }
//    }
//
//}

void EUART0_INT(void) interrupt 4 // EUART中断服务程序 - 只处理接收
{
	unsigned char xdata TempSBUF;

	// 只处理接收中断
	if (RI)
	{
		RI = 0;

		TempSBUF = SBUF;		// 读取接收数据寄存器
		Uart.RxdDataLength = 4; // Master data package : Fixed 4 Byte

		if (Uart.RxdFSM == 0x00)
		{
			switch (TempSBUF)
			{
			case HEAD_55: // 同步头(帧)0x55
				Uart.RxdData[Uart.RxdCnt] = TempSBUF;
				Uart.RxdCnt++;
				Uart.RxdFSM = 0x01;
				break;

			default:
				Uart.Cmd = 0x00;
				Uart.RxdCnt = 0x0;
				Uart.RxdFSM = 0x00;
				break;
			}
		}
		else if (Uart.RxdFSM == 0x01)
		{
			switch (TempSBUF)
			{
			case HEAD_AA: // 同步头(帧)0xAA
				Uart.RxdData[Uart.RxdCnt] = TempSBUF;
				Uart.RxdCnt++;
				Uart.RxdFSM = 0x02;
				break;

			default:
				Uart.Cmd = 0x00;
				Uart.RxdCnt = 0x0;
				Uart.RxdFSM = 0x00;
				break;
			}
		}
		else if (Uart.RxdFSM == 0x02)
		{
			Uart.RxdData[Uart.RxdCnt] = TempSBUF;
			Uart.RxdCnt++;

			if (Uart.RxdCnt >= Uart.RxdDataLength)
			{
				Uart.RxdResponseFlag = 0x01; // 接收完成标志
				Uart.RxdCnt = 0x00;
				Uart.RxdFSM = 0x00;
			}
		}
		else
		{
			Uart.RxdCnt = 0x00;
			Uart.RxdFSM = 0x00;
		}

		REN = 1; // 保持接收使能
	}

	// 清除发送中断标志（如果意外触发）
	if (TI)
	{
		TI = 0;
	}
}

/****************************************************************************
 * Function Description:UART_SendByte - 直接发送单字节
 * Input parameter     :dat - 要发送的字节
 * Output paramter     :void
 ****************************************************************************/
void UART_SendByte(unsigned char dat)
{
    while (!TI);  // 等待上一次发送完成
    TI = 0;       // 清除发送完成标志
    SBUF = dat;   // 发送数据
}

/****************************************************************************
 * Function Description:UART_SendBuffer - 直接发送数据缓冲区
 * Input parameter     :buffer - 数据缓冲区指针, length - 数据长度
 * Output paramter     :void
 ****************************************************************************/
void UART_SendBuffer(unsigned char *buffer, unsigned char length)
{
    unsigned char i;

    REN = 0;  // 发送期间禁用接收

    for (i = 0; i < length; i++)
    {
        UART_SendByte(buffer[i]);
    }

    REN = 1;  // 发送完成后重新使能接收
}

// void EUART0_INT(void) interrupt 4        						//EUART??????????
//{
//		unsigned char data TempSBUF;

//    if(RI)
//    {
//        RI = 0;
//
//				TempSBUF  = SBUF;         									//????????????
//				Uart.RxdDataLength = 11; 										//Master data package : Fixed 11 Byte
//
//	      if(Uart.RxdFSM == 0)
//		    {
//						switch(TempSBUF)
//						{
//								case HEAD:            						 	//????(?)0x55
//										Uart.RxdData[Uart.RxdCnt] = TempSBUF;
//										Uart.RxdCnt++;
//										Uart.RxdFSM = 0x01;
//								break;
//
//								default:
//										Uart.Cmd   = 0x00;
//										Uart.RxdCnt = 0x0;
//										Uart.RxdFSM = 0x00;
//								break;
//						}
//				}
//				else if(Uart.RxdFSM == 0x01)
//				{
//						switch(TempSBUF)
//						{
//								case CHOICE:												//0x10       /* ??? */
//								case STANDBY:												//0x11       /* ???? */
//								case STARTUP:  											//0x12       /* ????? */
//								case OPENLOOP:											//0x14       /* ??????????? */
//								case RURN:													//0xAA       /* ????????1 */
//								case ERROR:													//0x24       /* ERROR??? */
//								case ERRRESET:											//0xFE       /* ?????? */
//										Uart.Cmd = TempSBUF;
//										Uart.RxdData[Uart.RxdCnt] = TempSBUF;
//										Uart.RxdCnt++;
//										Uart.RxdFSM = 0x02;
//								break;
//
//								default:
//										Uart.Cmd = 0x00;
//										Uart.RxdCnt = 0x00;
//										Uart.RxdFSM = 0x00;
//								break;
//						}
//				}
//				else if(Uart.RxdFSM == 0x02)
//				{
//						Uart.RxdData[Uart.RxdCnt] = TempSBUF;
//						Uart.RxdCnt++;
//
//						if(Uart.RxdCnt >= Uart.RxdDataLength)
//						{
//								Uart.RxdResponseFlag = 0x01;			//????10?????ok!!!
//								Uart.RxdCnt = 0x00;
//								Uart.RxdFSM = 0x00;
//								REN = 0;													//???????
//						}
//				}
//				else
//				{
//						Uart.RxdCnt = 0x00;
//						Uart.RxdFSM = 0x00;
//					  REN = 1;													 		//???????
//				}
//    }

//
//		if(TI)
//    {
//				TI = 0;
//
//				if(Uart.TxdDataCnt < Uart.TxdDataLength)
//				{
//						Uart.TxdDataCnt++;
//						SBUF = Uart.TxdData[Uart.TxdDataCnt];  //????????????
//				}
//				else
//				{
//						REN = 1;													 			//???????
//				}
//    }
//
//}

/****************************************************************************
 * Function Description:Timer2_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void Timer2_INT(void) interrupt 5 // ???????2???????
{
	EXF2 = 0;
	TF2 = 0; // EXF2=T2CON.6 TF2=T2CON.7
}

/****************************************************************************
 * Function Description:EX2_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void EX2_INT(void) interrupt 9 // ??????2???????
{
	EXF0 = Bin(00000100);  // IE2=EXF0.0,????????2????????????BIT3??BIT2??=01,????????
	IEN1 &= Bin(11111011); // BIT2,EX2 = 0,?????????2
}

/****************************************************************************
 * Function Description:SCM_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void SCM_INT(void) interrupt 11
{
	CLKCON &= Bin(11101111); // SCMIF??????????????????
}

/****************************************************************************
 * Function Description:PWM_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void PWM_INT(void) interrupt 12 // PWM??????????
{
	PWMCON &= Bin(11111101); // PWMIF=0;
}

/****************************************************************************
 * Function Description:ELPD_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void ELPD_INT(void) interrupt 14 // LPD??????????
{
	LPDCON &= Bin(10111111); // LPDF=0;
}
