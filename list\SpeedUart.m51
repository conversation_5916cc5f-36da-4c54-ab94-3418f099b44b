BL51 BANKED LINKER/LOCATER V6.22.2.0                                                    06/24/2025  18:27:51  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22.2.0, INVOKED BY:
D:\KEIL_V5\C51\BIN\BL51.EXE .\output\STARTUP.obj, .\output\Main.obj, .\output\Mcu.obj, .\output\Interrupt.obj, .\output\
>> LedApp.obj, .\output\KeyApp.obj, .\output\Communication.obj, .\output\LedDriver.obj, .\output\KeyDriver.obj, .\output
>> \UartDriver.obj, .\output\PwmDriver.obj TO .\output\SpeedUart PRINT (.\list\SpeedUart.m51) RAMSIZE (256)


MEMORY MODEL: LARGE


INPUT MODULES INCLUDED:
  .\output\STARTUP.obj (?C_STARTUP)
  .\output\Main.obj (MAIN)
  .\output\Mcu.obj (MCU)
  .\output\Interrupt.obj (INTERRUPT)
  .\output\LedApp.obj (LEDAPP)
  .\output\KeyApp.obj (KEYAPP)
  .\output\Communication.obj (COMMUNICATION)
  .\output\LedDriver.obj (LEDDRIVER)
  .\output\KeyDriver.obj (KEYDRIVER)
  .\output\UartDriver.obj (UARTDRIVER)
  .\output\PwmDriver.obj (PWMDRIVER)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C_INIT)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?CLDOPTR)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?UIDIV)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?SLDIV)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?ULDIV)


LINK MAP OF MODULE:  .\output\SpeedUart (?C_STARTUP)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            IDATA   0008H     0001H     UNIT         ?STACK

            * * * * * * *  X D A T A   M E M O R Y  * * * * * * *
            XDATA   0000H     003BH     UNIT         ?XD?COMMUNICATION
            XDATA   003BH     0011H     UNIT         ?XD?INTERRUPT
            XDATA   004CH     000DH     UNIT         ?XD?KEYDRIVER
            XDATA   0059H     0005H     UNIT         _XDATA_GROUP_
            XDATA   005EH     0003H     UNIT         ?XD?LEDAPP
            XDATA   0061H     0002H     UNIT         ?XD?KEYAPP

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0003H     ABSOLUTE     
            CODE    0006H     0005H     UNIT         ?PR?EX0_INT?INTERRUPT
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0005H     UNIT         ?PR?EX1_INT?INTERRUPT
            CODE    0013H     0003H     ABSOLUTE     
            CODE    0016H     0005H     UNIT         ?PR?TIMER2_INT?INTERRUPT
            CODE    001BH     0003H     ABSOLUTE     
            CODE    001EH     0004H     UNIT         ?PR?SCM_INT?INTERRUPT
                    0022H     0001H                  *** GAP ***
            CODE    0023H     0003H     ABSOLUTE     
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 2


            CODE    0026H     0004H     UNIT         ?PR?PWM_INT?INTERRUPT
                    002AH     0001H                  *** GAP ***
            CODE    002BH     0003H     ABSOLUTE     
            CODE    002EH     001AH     UNIT         ?PR?INITT0?MCU
            CODE    0048H     0003H     UNIT         ?PR?INITCPUACSET?MCU
            CODE    004BH     0003H     ABSOLUTE     
            CODE    004EH     000DH     UNIT         ?PR?MONITORCPUTIMER?MCU
            CODE    005BH     0003H     ABSOLUTE     
            CODE    005EH     0004H     UNIT         ?PR?ELPD_INT?INTERRUPT
                    0062H     0001H                  *** GAP ***
            CODE    0063H     0003H     ABSOLUTE     
            CODE    0066H     000CH     UNIT         ?PR?INITINTERRUPTRAM?INTERRUPT
                    0072H     0001H                  *** GAP ***
            CODE    0073H     0003H     ABSOLUTE     
            CODE    0076H     0150H     UNIT         ?C?LIB_CODE
            CODE    01C6H     013DH     UNIT         ?PR?KEYSCAN?KEYDRIVER
            CODE    0303H     00D0H     UNIT         ?PR?MAIN?MAIN
            CODE    03D3H     00D0H     UNIT         ?PR?EUART0_INT?INTERRUPT
            CODE    04A3H     00BFH     UNIT         ?PR?_TXDDATAPROC?COMMUNICATION
            CODE    0562H     00BCH     UNIT         ?PR?TIMER0_INT?INTERRUPT
            CODE    061EH     008CH     UNIT         ?C_C51STARTUP
            CODE    06AAH     008BH     UNIT         ?PR?KEYPROC?KEYAPP
            CODE    0735H     0078H     UNIT         ?PR?TIMER1_INT?INTERRUPT
            CODE    07ADH     0076H     UNIT         ?PR?LEDAPP?LEDAPP
            CODE    0823H     0076H     UNIT         ?PR?COMMDEALRESPONSE?COMMUNICATION
            CODE    0899H     005CH     UNIT         ?PR?INITKEYSCANRAM?KEYDRIVER
            CODE    08F5H     0044H     UNIT         ?C?LDIV
            CODE    0939H     0040H     UNIT         ?PR?_UART0CALBAUDRATE?UARTDRIVER
            CODE    0979H     003AH     UNIT         ?PR?INTERRUPT
            CODE    09B3H     002CH     UNIT         ?PR?_CALCCHECKSUM?COMMUNICATION
            CODE    09DFH     0029H     UNIT         ?PR?UART0INIT?UARTDRIVER
            CODE    0A08H     0022H     UNIT         ?PR?KEYAPP
            CODE    0A2AH     0021H     UNIT         ?PR?_DELAYNMS?MCU
            CODE    0A4BH     001AH     UNIT         ?PR?COMMFUNPROC?COMMUNICATION
            CODE    0A65H     0019H     UNIT         ?PR?INITPORT?MCU
            CODE    0A7EH     0015H     UNIT         ?C_INITSEG
            CODE    0A93H     0014H     UNIT         ?PR?INITT1?MCU
            CODE    0AA7H     0013H     UNIT         ?PR?TIMER1_INIT?PWMDRIVER
            CODE    0ABAH     0012H     UNIT         ?PR?INITALLLEDOFF?LEDDRIVER
            CODE    0ACCH     000FH     UNIT         ?PR?COMMUNICATION
            CODE    0ADBH     000EH     UNIT         ?PR?INITSYS?MCU
            CODE    0AE9H     000CH     UNIT         ?PR?KEYDRIVER
            CODE    0AF5H     000AH     UNIT         ?PR?INITINTERRUPTPRIORITYLEVEL?MCU
            CODE    0AFFH     0009H     UNIT         ?PR?LED1_WORKON?LEDDRIVER
            CODE    0B08H     0009H     UNIT         ?PR?LED1_WORKOFF?LEDDRIVER
            CODE    0B11H     0009H     UNIT         ?PR?LED2_WORKON?LEDDRIVER
            CODE    0B1AH     0009H     UNIT         ?PR?LED2_WORKOFF?LEDDRIVER
            CODE    0B23H     0009H     UNIT         ?PR?LED3_WORKON?LEDDRIVER
            CODE    0B2CH     0009H     UNIT         ?PR?LED3_WORKOFF?LEDDRIVER
            CODE    0B35H     0009H     UNIT         ?PR?LED4_WORKON?LEDDRIVER
            CODE    0B3EH     0009H     UNIT         ?PR?LED4_WORKOFF?LEDDRIVER
            CODE    0B47H     0009H     UNIT         ?PR?LED5_WORKON?LEDDRIVER
            CODE    0B50H     0009H     UNIT         ?PR?LED5_WORKOFF?LEDDRIVER
            CODE    0B59H     0009H     UNIT         ?PR?LED6_WORKON?LEDDRIVER
            CODE    0B62H     0009H     UNIT         ?PR?LED6_WORKOFF?LEDDRIVER
            CODE    0B6BH     0008H     UNIT         ?PR?INITPWM?MCU
            CODE    0B73H     0007H     UNIT         ?PR?INITINT1?MCU
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 3


            CODE    0B7AH     0007H     UNIT         ?PR?INITINT2?MCU
            CODE    0B81H     0007H     UNIT         ?PR?EX2_INT?INTERRUPT
            CODE    0B88H     0006H     UNIT         ?PR?INITKEYPROCRAM?KEYAPP
            CODE    0B8EH     0006H     UNIT         ?PR?INITALLLEDDRIVERRAM?LEDDRIVER
            CODE    0B94H     0003H     UNIT         ?PR?INITALLLEDRAMAPP?LEDAPP



OVERLAY MAP OF MODULE:   .\output\SpeedUart (?C_STARTUP)


SEGMENT                                      XDATA_GROUP
  +--> CALLED SEGMENT                      START    LENGTH
----------------------------------------------------------
?C_C51STARTUP                              -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                              -----    -----
  +--> ?PR?INITSYS?MCU
  +--> ?PR?_DELAYNMS?MCU
  +--> ?PR?INITPORT?MCU
  +--> ?PR?INITKEYSCANRAM?KEYDRIVER
  +--> ?PR?INITKEYPROCRAM?KEYAPP
  +--> ?PR?INITINTERRUPTRAM?INTERRUPT
  +--> ?PR?INITALLLEDOFF?LEDDRIVER
  +--> ?PR?INITALLLEDRAMAPP?LEDAPP
  +--> ?PR?INITT0?MCU
  +--> ?PR?INITT1?MCU
  +--> ?PR?INITINT1?MCU
  +--> ?PR?INITINT2?MCU
  +--> ?PR?INITPWM?MCU
  +--> ?PR?UART0INIT?UARTDRIVER
  +--> ?PR?TIMER1_INIT?PWMDRIVER
  +--> ?PR?MONITORCPUTIMER?MCU
  +--> ?PR?INITCPUACSET?MCU
  +--> ?PR?KEYSCAN?KEYDRIVER
  +--> ?PR?KEYPROC?KEYAPP
  +--> ?PR?LEDAPP?LEDAPP
  +--> ?PR?COMMFUNPROC?COMMUNICATION

?PR?INITSYS?MCU                            -----    -----
  +--> ?PR?INITINTERRUPTPRIORITYLEVEL?MCU

?PR?INITINTERRUPTRAM?INTERRUPT             -----    -----
  +--> ?PR?INTERRUPT

?PR?INITALLLEDOFF?LEDDRIVER                -----    -----
  +--> ?PR?LED1_WORKOFF?LEDDRIVER
  +--> ?PR?LED2_WORKOFF?LEDDRIVER
  +--> ?PR?LED3_WORKOFF?LEDDRIVER
  +--> ?PR?LED4_WORKOFF?LEDDRIVER
  +--> ?PR?LED5_WORKOFF?LEDDRIVER
  +--> ?PR?LED6_WORKOFF?LEDDRIVER

?PR?INITALLLEDRAMAPP?LEDAPP                -----    -----
  +--> ?PR?INITALLLEDDRIVERRAM?LEDDRIVER
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 4



?PR?UART0INIT?UARTDRIVER                   -----    -----
  +--> ?PR?_UART0CALBAUDRATE?UARTDRIVER

?PR?_UART0CALBAUDRATE?UARTDRIVER           0059H    0002H

?PR?KEYSCAN?KEYDRIVER                      0059H    0002H
  +--> ?PR?KEYDRIVER

?PR?KEYPROC?KEYAPP                         -----    -----
  +--> ?PR?KEYAPP
  +--> ?PR?_TXDDATAPROC?COMMUNICATION

?PR?_TXDDATAPROC?COMMUNICATION             0059H    0001H
  +--> ?PR?COMMUNICATION
  +--> ?PR?_CALCCHECKSUM?COMMUNICATION

?PR?_CALCCHECKSUM?COMMUNICATION            005AH    0003H

?PR?LEDAPP?LEDAPP                          -----    -----
  +--> ?PR?INITALLLEDOFF?LEDDRIVER
  +--> ?PR?LED1_WORKON?LEDDRIVER
  +--> ?PR?LED2_WORKON?LEDDRIVER
  +--> ?PR?LED3_WORKON?LEDDRIVER
  +--> ?PR?LED4_WORKON?LEDDRIVER
  +--> ?PR?LED5_WORKON?LEDDRIVER
  +--> ?PR?LED6_WORKON?LEDDRIVER

?PR?COMMFUNPROC?COMMUNICATION              -----    -----
  +--> ?PR?_TXDDATAPROC?COMMUNICATION
  +--> ?PR?COMMDEALRESPONSE?COMMUNICATION

?PR?COMMDEALRESPONSE?COMMUNICATION         -----    -----
  +--> ?PR?_CALCCHECKSUM?COMMUNICATION
  +--> ?PR?_TXDDATAPROC?COMMUNICATION

*** NEW ROOT ***************************************************

?PR?TIMER0_INT?INTERRUPT                   -----    -----
  +--> ?PR?INTERRUPT

*** NEW ROOT ***************************************************

?PR?EUART0_INT?INTERRUPT                   005DH    0001H
  +--> ?PR?INTERRUPT



SYMBOL TABLE OF MODULE:  .\output\SpeedUart (?C_STARTUP)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        ?C_STARTUP
  C:061EH         SEGMENT       ?C_C51STARTUP
  I:0008H         SEGMENT       ?STACK
  C:0000H         PUBLIC        ?C_STARTUP
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 5


  D:00E0H         SYMBOL        ACC
  D:00F0H         SYMBOL        B
  N:3026H         SYMBOL        CODE_SIZE
  D:0083H         SYMBOL        DPH
  D:0082H         SYMBOL        DPL
  N:0F9AH         SYMBOL        FILLING_A5_NUM
  C:062AH         SYMBOL        FILL_CODE
  N:0000H         SYMBOL        IBPSTACK
  N:0100H         SYMBOL        IBPSTACKTOP
  N:0080H         SYMBOL        IDATALEN
  C:0621H         SYMBOL        IDATALOOP
  N:0000H         SYMBOL        PBPSTACK
  N:0000H         SYMBOL        PBPSTACKTOP
  N:0000H         SYMBOL        PDATALEN
  N:0000H         SYMBOL        PDATASTART
  N:0000H         SYMBOL        PPAGE
  N:0000H         SYMBOL        PPAGEENABLE
  D:00A0H         SYMBOL        PPAGE_SFR
  N:3FC0H         SYMBOL        ROM_SIZE
  D:0081H         SYMBOL        SP
  C:061EH         SYMBOL        STARTUP1
  N:0000H         SYMBOL        XBPSTACK
  N:0000H         SYMBOL        XBPSTACKTOP
  N:0000H         SYMBOL        XDATALEN
  N:0000H         SYMBOL        XDATASTART
  C:0000H         LINE#         107
  C:061EH         LINE#         114
  C:0620H         LINE#         115
  C:0621H         LINE#         116
  C:0622H         LINE#         117
  C:0624H         LINE#         166
  C:0627H         LINE#         170
  -------         ENDMOD        ?C_STARTUP

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IEN0
  D:00A9H         PUBLIC        IEN1
  C:0303H         PUBLIC        main
  -------         PROC          MAIN
  C:0303H         LINE#         50
  C:0303H         LINE#         51
  C:0303H         LINE#         52
  C:0305H         LINE#         53
  C:0308H         LINE#         54
  C:030FH         LINE#         55
  C:0312H         LINE#         56
  C:0315H         LINE#         57
  C:0318H         LINE#         58
  C:031BH         LINE#         59
  C:031EH         LINE#         60
  C:0321H         LINE#         61
  C:0324H         LINE#         62
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 6


  C:0327H         LINE#         63
  C:032AH         LINE#         64
  C:032DH         LINE#         65
  C:0330H         LINE#         66
  C:0333H         LINE#         67
  C:0336H         LINE#         68
  C:0339H         LINE#         69
  C:033CH         LINE#         70
  C:033EH         LINE#         81
  C:033EH         LINE#         82
  C:033EH         LINE#         83
  C:0341H         LINE#         84
  C:0344H         LINE#         85
  C:0347H         LINE#         86
  C:034AH         LINE#         87
  C:034DH         LINE#         88
  C:0350H         LINE#         89
  C:0353H         LINE#         92
  C:035CH         LINE#         95
  C:0367H         LINE#         98
  C:036AH         LINE#         100
  C:0374H         LINE#         101
  C:0374H         LINE#         102
  C:0376H         LINE#         103
  C:0381H         LINE#         104
  C:0384H         LINE#         105
  C:0386H         LINE#         106
  C:0391H         LINE#         107
  C:0394H         LINE#         108
  C:0396H         LINE#         109
  C:03A1H         LINE#         110
  C:03A4H         LINE#         111
  C:03A6H         LINE#         112
  C:03B1H         LINE#         113
  C:03B4H         LINE#         114
  C:03B6H         LINE#         115
  C:03C1H         LINE#         116
  C:03C4H         LINE#         117
  C:03C6H         LINE#         118
  C:03C6H         LINE#         119
  C:03C9H         LINE#         120
  C:03C9H         LINE#         121
  C:03C9H         LINE#         124
  C:03D0H         LINE#         125
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        MCU
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00EAH         PUBLIC        P1M0
  D:00E2H         PUBLIC        P1M1
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00EDH         PUBLIC        P4M0
  D:00E4H         PUBLIC        P3M1
  D:00E5H         PUBLIC        P4M1
  D:00B0H         PUBLIC        P3
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 7


  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  D:00A9H         PUBLIC        IEN1
  D:00B4H         PUBLIC        IPH0
  D:00B5H         PUBLIC        IPH1
  D:00E8H         PUBLIC        EXF0
  D:00B8H         PUBLIC        IPL0
  D:00B9H         PUBLIC        IPL1
  C:002EH         PUBLIC        InitT0
  C:004EH         PUBLIC        MonitorCpuTimer
  C:0A93H         PUBLIC        InitT1
  C:0A2AH         PUBLIC        _DelayNms
  C:0B73H         PUBLIC        InitINT1
  C:0B7AH         PUBLIC        InitINT2
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:00B1H         PUBLIC        RSTSTAT
  D:00D3H         PUBLIC        PWMD
  D:00B2H         PUBLIC        CLKCON
  C:0A65H         PUBLIC        InitPort
  B:0088H.3       PUBLIC        IE1
  D:00B3H         PUBLIC        LPDCON
  D:00D2H         PUBLIC        PWMP
  B:0088H.5       PUBLIC        TF0
  B:0088H.7       PUBLIC        TF1
  C:0B6BH         PUBLIC        InitPwm
  D:008CH         PUBLIC        TH0
  D:008DH         PUBLIC        TH1
  B:0088H.2       PUBLIC        IT1
  B:00A8H.2       PUBLIC        EX1
  C:0AF5H         PUBLIC        InitInterruptPriorityLevel
  D:008AH         PUBLIC        TL0
  D:008BH         PUBLIC        TL1
  B:0088H.4       PUBLIC        TR0
  D:00D1H         PUBLIC        PWMCON
  C:0ADBH         PUBLIC        InitSys
  B:0088H.6       PUBLIC        TR1
  D:00CEH         PUBLIC        TCON1
  C:0048H         PUBLIC        InitCpuACSet
  -------         PROC          INITSYS
  C:0ADBH         LINE#         50
  C:0ADBH         LINE#         51
  C:0ADBH         LINE#         52
  C:0ADDH         LINE#         53
  C:0AE0H         LINE#         54
  C:0AE2H         LINE#         55
  C:0AE5H         LINE#         56
  C:0AE8H         LINE#         57
  -------         ENDPROC       INITSYS
  -------         PROC          INITCPUACSET
  C:0048H         LINE#         64
  C:0048H         LINE#         65
  C:0048H         LINE#         66
  C:004AH         LINE#         67
  -------         ENDPROC       INITCPUACSET
  -------         PROC          INITPORT
  C:0A65H         LINE#         74
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 8


  C:0A65H         LINE#         75
  C:0A65H         LINE#         76
  C:0A68H         LINE#         77
  C:0A6BH         LINE#         78
  C:0A6DH         LINE#         80
  C:0A70H         LINE#         81
  C:0A73H         LINE#         83
  C:0A75H         LINE#         84
  C:0A78H         LINE#         86
  C:0A7AH         LINE#         87
  C:0A7DH         LINE#         88
  -------         ENDPROC       INITPORT
  -------         PROC          INITINTERRUPTPRIORITYLEVEL
  C:0AF5H         LINE#         96
  C:0AF5H         LINE#         97
  C:0AF5H         LINE#         98
  C:0AF8H         LINE#         99
  C:0AFAH         LINE#         100
  C:0AFCH         LINE#         101
  C:0AFEH         LINE#         102
  -------         ENDPROC       INITINTERRUPTPRIORITYLEVEL
  -------         PROC          INITT0
  C:002EH         LINE#         109
  C:002EH         LINE#         110
  C:002EH         LINE#         111
  C:0031H         LINE#         113
  C:0034H         LINE#         114
  C:0037H         LINE#         116
  C:003AH         LINE#         117
  C:003DH         LINE#         119
  C:0040H         LINE#         120
  C:0043H         LINE#         121
  C:0045H         LINE#         122
  C:0047H         LINE#         123
  -------         ENDPROC       INITT0
  -------         PROC          INITT1
  C:0A93H         LINE#         131
  C:0A93H         LINE#         132
  C:0A93H         LINE#         133
  C:0A96H         LINE#         135
  C:0A99H         LINE#         136
  C:0A99H         LINE#         139
  C:0A9CH         LINE#         140
  C:0A9CH         LINE#         143
  C:0A9FH         LINE#         144
  C:0AA2H         LINE#         145
  C:0AA4H         LINE#         146
  C:0AA6H         LINE#         147
  -------         ENDPROC       INITT1
  -------         PROC          INITINT1
  C:0B73H         LINE#         155
  C:0B73H         LINE#         156
  C:0B73H         LINE#         157
  C:0B75H         LINE#         158
  C:0B77H         LINE#         159
  C:0B79H         LINE#         160
  -------         ENDPROC       INITINT1
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 9


  -------         PROC          INITINT2
  C:0B7AH         LINE#         167
  C:0B7AH         LINE#         168
  C:0B7AH         LINE#         169
  C:0B7DH         LINE#         170
  C:0B80H         LINE#         171
  -------         ENDPROC       INITINT2
  -------         PROC          INITPWM
  C:0B6BH         LINE#         179
  C:0B6BH         LINE#         180
  C:0B6BH         LINE#         185
  C:0B6EH         LINE#         186
  C:0B70H         LINE#         187
  C:0B72H         LINE#         188
  -------         ENDPROC       INITPWM
  -------         PROC          MONITORCPUTIMER
  C:004EH         LINE#         196
  C:004EH         LINE#         197
  C:004EH         LINE#         198
  C:0056H         LINE#         199
  C:0056H         LINE#         200
  C:0057H         LINE#         202
  C:005AH         LINE#         203
  C:005AH         LINE#         204
  -------         ENDPROC       MONITORCPUTIMER
  -------         PROC          _DELAYNMS
  D:0006H         SYMBOL        num
  -------         DO            
  D:0004H         SYMBOL        i
  D:0002H         SYMBOL        j
  -------         ENDDO         
  C:0A2AH         LINE#         212
  C:0A2AH         LINE#         213
  C:0A2AH         LINE#         216
  C:0A34H         LINE#         217
  C:0A34H         LINE#         218
  C:0A37H         LINE#         219
  C:0A37H         LINE#         220
  C:0A38H         LINE#         221
  C:0A43H         LINE#         222
  C:0A4AH         LINE#         223
  -------         ENDPROC       _DELAYNMS
  -------         ENDMOD        MCU

  -------         MODULE        INTERRUPT
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:03D3H         PUBLIC        EUART0_INT
  C:0562H         PUBLIC        Timer0_INT
  D:0090H         PUBLIC        P1
  C:0735H         PUBLIC        Timer1_INT
  C:0016H         PUBLIC        Timer2_INT
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:0066H         PUBLIC        InitInterruptRam
  D:00A9H         PUBLIC        IEN1
  D:00E8H         PUBLIC        EXF0
  B:00C8H.6       PUBLIC        EXF2
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 10


  C:0006H         PUBLIC        EX0_INT
  C:000EH         PUBLIC        EX1_INT
  B:0098H.0       PUBLIC        RI
  C:0B81H         PUBLIC        EX2_INT
  B:0098H.1       PUBLIC        TI
  X:003BH         PUBLIC        count
  X:003CH         PUBLIC        high_count
  C:001EH         PUBLIC        SCM_INT
  D:0099H         PUBLIC        SBUF
  X:003DH         PUBLIC        duty_shadow
  D:00B2H         PUBLIC        CLKCON
  B:0088H.1       PUBLIC        IE0
  B:0088H.3       PUBLIC        IE1
  C:0026H         PUBLIC        PWM_INT
  D:00B3H         PUBLIC        LPDCON
  X:003EH         PUBLIC        duty
  X:003FH         PUBLIC        TimeFlagStr
  B:0088H.5       PUBLIC        TF0
  B:0088H.7       PUBLIC        TF1
  B:00C8H.7       PUBLIC        TF2
  D:008CH         PUBLIC        TH0
  B:00A8H.0       PUBLIC        EX0
  D:008DH         PUBLIC        TH1
  B:00A8H.2       PUBLIC        EX1
  D:008AH         PUBLIC        TL0
  D:008BH         PUBLIC        TL1
  B:0088H.4       PUBLIC        TR0
  D:00D1H         PUBLIC        PWMCON
  B:0098H.4       PUBLIC        REN
  B:00B0H.0       PUBLIC        PWM_IN
  C:005EH         PUBLIC        ELPD_INT
  C:0979H         SYMBOL        Com0029
  C:0979H         SYMBOL        L?0042
  C:0985H         SYMBOL        L?0043
  C:0993H         SYMBOL        L?0044
  C:099BH         SYMBOL        L?0045
  C:099EH         SYMBOL        L?0046
  C:09A8H         SYMBOL        L?0047
  -------         PROC          COM0029
  -------         ENDPROC       COM0029
  -------         PROC          INITINTERRUPTRAM
  C:0066H         LINE#         52
  C:0066H         LINE#         53
  C:0066H         LINE#         54
  C:006AH         LINE#         56
  C:006AH         LINE#         57
  C:006AH         LINE#         58
  C:006AH         LINE#         60
  C:006AH         LINE#         61
  C:006DH         LINE#         63
  C:006EH         LINE#         64
  C:006EH         LINE#         66
  C:006EH         LINE#         67
  C:0071H         LINE#         68
  -------         ENDPROC       INITINTERRUPTRAM
  -------         PROC          EX0_INT
  C:0006H         LINE#         75
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 11


  C:0006H         LINE#         77
  C:0008H         LINE#         78
  C:000AH         LINE#         79
  -------         ENDPROC       EX0_INT
  -------         PROC          TIMER0_INT
  C:0562H         LINE#         86
  C:056AH         LINE#         88
  C:056CH         LINE#         89
  C:056EH         LINE#         90
  C:0571H         LINE#         91
  C:0574H         LINE#         92
  C:0576H         LINE#         98
  C:057CH         LINE#         100
  C:0582H         LINE#         101
  C:058FH         LINE#         102
  C:058FH         LINE#         103
  C:0591H         LINE#         105
  C:0592H         LINE#         106
  C:0597H         LINE#         107
  C:0597H         LINE#         108
  C:0599H         LINE#         109
  C:0599H         LINE#         111
  C:059FH         LINE#         112
  C:05A9H         LINE#         113
  C:05A9H         LINE#         114
  C:05ABH         LINE#         115
  C:05AEH         LINE#         117
  C:05B2H         LINE#         119
  C:05B5H         LINE#         120
  C:05BAH         LINE#         121
  C:05BAH         LINE#         122
  C:05BCH         LINE#         123
  C:05BFH         LINE#         125
  C:05C5H         LINE#         126
  C:05C8H         LINE#         127
  C:05C8H         LINE#         128
  C:05CBH         LINE#         129
  C:05CDH         LINE#         130
  C:05CDH         LINE#         133
  C:05DBH         LINE#         134
  C:05EAH         LINE#         135
  C:05EAH         LINE#         136
  C:05EEH         LINE#         137
  C:05F1H         LINE#         139
  C:05FFH         LINE#         140
  C:060EH         LINE#         141
  C:060EH         LINE#         142
  C:0612H         LINE#         143
  C:0615H         LINE#         144
  C:0615H         LINE#         145
  C:0615H         LINE#         146
  C:0615H         LINE#         171
  C:0615H         LINE#         172
  -------         ENDPROC       TIMER0_INT
  -------         PROC          EX1_INT
  C:000EH         LINE#         179
  C:000EH         LINE#         181
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 12


  C:0010H         LINE#         182
  C:0012H         LINE#         183
  -------         ENDPROC       EX1_INT
  -------         PROC          TIMER1_INT
  C:0735H         LINE#         190
  C:074CH         LINE#         192
  C:074EH         LINE#         195
  C:0751H         LINE#         196
  C:0754H         LINE#         198
  C:075AH         LINE#         199
  C:0761H         LINE#         201
  C:076FH         LINE#         203
  C:0779H         LINE#         204
  C:0791H         LINE#         205
  C:0791H         LINE#         208
  C:0796H         LINE#         209
  C:0798H         LINE#         210
  C:0798H         LINE#         211
  -------         ENDPROC       TIMER1_INT
  -------         PROC          EUART0_INT
  -------         DO            
  X:005DH         SYMBOL        TempSBUF
  -------         ENDDO         
  C:03D3H         LINE#         307
  C:03E2H         LINE#         311
  C:03E8H         LINE#         312
  C:03E8H         LINE#         313
  C:03EAH         LINE#         315
  C:03F0H         LINE#         316
  C:03F6H         LINE#         318
  C:03FDH         LINE#         319
  C:03FDH         LINE#         320
  C:0406H         LINE#         321
  C:0406H         LINE#         322
  C:0406H         LINE#         323
  C:0414H         LINE#         324
  C:0417H         LINE#         325
  C:041DH         LINE#         327
  C:0420H         LINE#         329
  C:0420H         LINE#         334
  C:0420H         LINE#         335
  C:0420H         LINE#         336
  C:0425H         LINE#         337
  C:0425H         LINE#         338
  C:042EH         LINE#         339
  C:042EH         LINE#         340
  C:042EH         LINE#         341
  C:042EH         LINE#         342
  C:0431H         LINE#         343
  C:0437H         LINE#         345
  C:0439H         LINE#         347
  C:0439H         LINE#         348
  C:043EH         LINE#         349
  C:0442H         LINE#         350
  C:0446H         LINE#         351
  C:0448H         LINE#         352
  C:0448H         LINE#         353
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 13


  C:0448H         LINE#         354
  C:0450H         LINE#         355
  C:0450H         LINE#         356
  C:0455H         LINE#         357
  C:0458H         LINE#         359
  C:0463H         LINE#         360
  C:0463H         LINE#         361
  C:0466H         LINE#         362
  C:0466H         LINE#         363
  C:0469H         LINE#         365
  C:046BH         LINE#         366
  C:046BH         LINE#         367
  C:046DH         LINE#         369
  C:046DH         LINE#         370
  C:046DH         LINE#         371
  C:0470H         LINE#         372
  C:0472H         LINE#         373
  C:0472H         LINE#         374
  C:0472H         LINE#         376
  C:0475H         LINE#         377
  C:0475H         LINE#         378
  C:0477H         LINE#         380
  C:0482H         LINE#         381
  C:0482H         LINE#         382
  C:0485H         LINE#         383
  C:0492H         LINE#         384
  C:0494H         LINE#         386
  C:0494H         LINE#         387
  C:0496H         LINE#         388
  C:0496H         LINE#         389
  C:0496H         LINE#         390
  -------         ENDPROC       EUART0_INT
  -------         PROC          TIMER2_INT
  C:0016H         LINE#         495
  C:0016H         LINE#         497
  C:0018H         LINE#         498
  C:001AH         LINE#         499
  -------         ENDPROC       TIMER2_INT
  -------         PROC          EX2_INT
  C:0B81H         LINE#         506
  C:0B81H         LINE#         508
  C:0B84H         LINE#         509
  C:0B87H         LINE#         510
  -------         ENDPROC       EX2_INT
  -------         PROC          SCM_INT
  C:001EH         LINE#         517
  C:001EH         LINE#         519
  C:0021H         LINE#         520
  -------         ENDPROC       SCM_INT
  -------         PROC          PWM_INT
  C:0026H         LINE#         527
  C:0026H         LINE#         529
  C:0029H         LINE#         530
  -------         ENDPROC       PWM_INT
  -------         PROC          ELPD_INT
  C:005EH         LINE#         537
  C:005EH         LINE#         539
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 14


  C:0061H         LINE#         540
  -------         ENDPROC       ELPD_INT
  -------         ENDMOD        INTERRUPT

  -------         MODULE        LEDAPP
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:005EH         PUBLIC        LedWorkGroupStr
  C:0B94H         PUBLIC        InitAllLedRamApp
  C:07ADH         PUBLIC        LedApp
  -------         PROC          INITALLLEDRAMAPP
  C:0B94H         LINE#         50
  C:0B94H         LINE#         51
  C:0B94H         LINE#         52
  -------         ENDPROC       INITALLLEDRAMAPP
  -------         PROC          LEDAPP
  C:07ADH         LINE#         60
  C:07ADH         LINE#         61
  C:07ADH         LINE#         62
  C:07B5H         LINE#         63
  C:07B5H         LINE#         64
  C:07B6H         LINE#         66
  C:07CEH         LINE#         67
  C:07CEH         LINE#         68
  C:07CEH         LINE#         69
  C:07D1H         LINE#         70
  C:07D4H         LINE#         71
  C:07D4H         LINE#         73
  C:07D4H         LINE#         74
  C:07D7H         LINE#         75
  C:07DAH         LINE#         76
  C:07DDH         LINE#         77
  C:07DDH         LINE#         79
  C:07DDH         LINE#         80
  C:07E0H         LINE#         81
  C:07E3H         LINE#         82
  C:07E6H         LINE#         83
  C:07E9H         LINE#         84
  C:07E9H         LINE#         86
  C:07E9H         LINE#         87
  C:07ECH         LINE#         88
  C:07EFH         LINE#         89
  C:07F2H         LINE#         90
  C:07F5H         LINE#         91
  C:07F8H         LINE#         92
  C:07F8H         LINE#         94
  C:07F8H         LINE#         95
  C:07FBH         LINE#         96
  C:07FEH         LINE#         97
  C:0801H         LINE#         98
  C:0804H         LINE#         99
  C:0807H         LINE#         100
  C:080AH         LINE#         101
  C:080AH         LINE#         103
  C:080AH         LINE#         104
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 15


  C:080DH         LINE#         105
  C:0810H         LINE#         106
  C:0813H         LINE#         107
  C:0816H         LINE#         108
  C:0819H         LINE#         109
  C:081CH         LINE#         110
  C:081FH         LINE#         111
  C:081FH         LINE#         113
  C:081FH         LINE#         114
  C:0822H         LINE#         115
  C:0822H         LINE#         116
  C:0822H         LINE#         117
  C:0822H         LINE#         118
  -------         ENDPROC       LEDAPP
  -------         ENDMOD        LEDAPP

  -------         MODULE        KEYAPP
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:06AAH         PUBLIC        KeyProc
  B:0098H.4       PUBLIC        REN
  X:0061H         PUBLIC        g_last_speed_level
  C:0B88H         PUBLIC        InitKeyProcRam
  X:0062H         PUBLIC        KeyProcStr
  C:0A08H         SYMBOL        Com000F
  C:0A08H         SYMBOL        L?0016
  -------         PROC          COM000F
  -------         ENDPROC       COM000F
  -------         PROC          INITKEYPROCRAM
  C:0B88H         LINE#         40
  C:0B88H         LINE#         41
  C:0B88H         LINE#         42
  C:0B8DH         LINE#         43
  -------         ENDPROC       INITKEYPROCRAM
  C:0729H         SYMBOL        L?0017
  -------         PROC          KEYPROC
  C:06AAH         LINE#         50
  C:06AAH         LINE#         51
  C:06AAH         LINE#         52
  C:06B2H         LINE#         53
  C:06B2H         LINE#         54
  C:06B3H         LINE#         56
  C:06BBH         LINE#         57
  C:06BBH         LINE#         58
  C:06BCH         LINE#         59
  C:06C4H         LINE#         60
  C:06C4H         LINE#         61
  C:06C5H         LINE#         64
  C:06CBH         LINE#         65
  C:06D5H         LINE#         66
  C:06D5H         LINE#         67
  C:06D8H         LINE#         68
  C:06D8H         LINE#         70
  C:06D8H         LINE#         72
  C:06D8H         LINE#         73
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 16


  C:06D8H         LINE#         74
  C:06D8H         LINE#         75
  C:06D8H         LINE#         78
  C:06DDH         LINE#         79
  C:06DDH         LINE#         80
  C:06DDH         LINE#         81
  C:06E0H         LINE#         82
  C:06E0H         LINE#         83
  C:06E0H         LINE#         84
  C:06E0H         LINE#         85
  C:06E0H         LINE#         87
  C:06E8H         LINE#         88
  C:06E8H         LINE#         89
  C:06E9H         LINE#         91
  C:06F1H         LINE#         92
  C:06F1H         LINE#         93
  C:06F2H         LINE#         94
  C:06FAH         LINE#         95
  C:06FAH         LINE#         96
  C:06FBH         LINE#         98
  C:070BH         LINE#         99
  C:070BH         LINE#         100
  C:070EH         LINE#         101
  C:0710H         LINE#         102
  C:071BH         LINE#         103
  C:071BH         LINE#         104
  C:0720H         LINE#         105
  C:0720H         LINE#         107
  C:0720H         LINE#         109
  C:0720H         LINE#         110
  C:0720H         LINE#         111
  C:0720H         LINE#         112
  C:0720H         LINE#         115
  C:0725H         LINE#         116
  C:0725H         LINE#         117
  C:0725H         LINE#         118
  C:0728H         LINE#         119
  C:0728H         LINE#         120
  C:0728H         LINE#         121
  C:0728H         LINE#         122
  C:0728H         LINE#         123
  -------         ENDPROC       KEYPROC
  -------         ENDMOD        KEYAPP

  -------         MODULE        COMMUNICATION
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:0000H         PUBLIC        Uart
  C:09B3H         PUBLIC        _CalcChecksum
  D:0099H         PUBLIC        SBUF
  C:04A3H         PUBLIC        _TxdDataProc
  C:0A4BH         PUBLIC        CommFunProc
  C:0823H         PUBLIC        CommDealResponse
  B:0098H.4       PUBLIC        REN
  C:0ACCH         SYMBOL        Com003C
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 17


  C:0ACCH         SYMBOL        L?0061
  C:0ACFH         SYMBOL        L?0062
  -------         PROC          COM003C
  -------         ENDPROC       COM003C
  -------         PROC          _CALCCHECKSUM
  X:005AH         SYMBOL        CS_Buffer
  D:0005H         SYMBOL        Len
  -------         DO            
  D:0007H         SYMBOL        CS
  D:0006H         SYMBOL        j
  -------         ENDDO         
  C:09B3H         LINE#         104
  C:09BEH         LINE#         105
  C:09BEH         LINE#         106
  C:09C0H         LINE#         107
  C:09C1H         LINE#         109
  C:09C6H         LINE#         110
  C:09C6H         LINE#         111
  C:09DBH         LINE#         112
  C:09DEH         LINE#         113
  C:09DEH         LINE#         114
  -------         ENDPROC       _CALCCHECKSUM
  -------         PROC          COMMFUNPROC
  C:0A4BH         LINE#         121
  C:0A4BH         LINE#         122
  C:0A4BH         LINE#         123
  C:0A53H         LINE#         124
  C:0A53H         LINE#         125
  C:0A54H         LINE#         153
  C:0A59H         LINE#         156
  C:0A61H         LINE#         157
  C:0A61H         LINE#         158
  C:0A64H         LINE#         159
  C:0A64H         LINE#         161
  C:0A64H         LINE#         162
  -------         ENDPROC       COMMFUNPROC
  C:0556H         SYMBOL        L?0063
  -------         PROC          _TXDDATAPROC
  X:0059H         SYMBOL        Ack
  -------         DO            
  D:0001H         SYMBOL        checksum
  -------         ENDDO         
  C:04A3H         LINE#         169
  C:04A8H         LINE#         170
  C:04A8H         LINE#         172
  C:04A8H         LINE#         174
  C:04ADH         LINE#         175
  C:04ADH         LINE#         176
  C:04AFH         LINE#         177
  C:04AFH         LINE#         179
  C:04C4H         LINE#         180
  C:04C4H         LINE#         181
  C:04C4H         LINE#         182
  C:04C4H         LINE#         183
  C:04C4H         LINE#         184
  C:04C4H         LINE#         185
  C:04C4H         LINE#         186
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 18


  C:04C6H         LINE#         188
  C:04C6H         LINE#         189
  C:04CAH         LINE#         190
  C:04CCH         LINE#         192
  C:04CCH         LINE#         193
  C:04D1H         LINE#         194
  C:04D1H         LINE#         195
  C:04D1H         LINE#         198
  C:04D1H         LINE#         199
  C:04D1H         LINE#         207
  C:04D3H         LINE#         208
  C:04DBH         LINE#         209
  C:04DBH         LINE#         210
  C:04DDH         LINE#         212
  C:04DDH         LINE#         214
  C:04F2H         LINE#         215
  C:04F2H         LINE#         216
  C:04F2H         LINE#         217
  C:04F2H         LINE#         218
  C:04F2H         LINE#         219
  C:04F2H         LINE#         220
  C:04F2H         LINE#         221
  C:04F4H         LINE#         223
  C:04F4H         LINE#         224
  C:04F8H         LINE#         225
  C:04FAH         LINE#         227
  C:04FAH         LINE#         228
  C:04FFH         LINE#         229
  C:04FFH         LINE#         230
  C:04FFH         LINE#         233
  C:0504H         LINE#         234
  C:0509H         LINE#         242
  C:050BH         LINE#         243
  C:0513H         LINE#         244
  C:0513H         LINE#         245
  C:0518H         LINE#         247
  C:0518H         LINE#         249
  C:052DH         LINE#         250
  C:052DH         LINE#         251
  C:052DH         LINE#         252
  C:052DH         LINE#         253
  C:052DH         LINE#         254
  C:052DH         LINE#         255
  C:052DH         LINE#         256
  C:052FH         LINE#         258
  C:052FH         LINE#         259
  C:0533H         LINE#         260
  C:0535H         LINE#         262
  C:0535H         LINE#         263
  C:053AH         LINE#         264
  C:053AH         LINE#         265
  C:053AH         LINE#         268
  C:053DH         LINE#         269
  C:0542H         LINE#         277
  C:0542H         LINE#         279
  C:0547H         LINE#         280
  C:054DH         LINE#         281
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 19


  C:054FH         LINE#         282
  C:0555H         LINE#         283
  -------         ENDPROC       _TXDDATAPROC
  -------         PROC          COMMDEALRESPONSE
  -------         DO            
  D:0007H         SYMBOL        rechecksum
  -------         ENDDO         
  C:0823H         LINE#         290
  C:0823H         LINE#         291
  C:0823H         LINE#         294
  C:082BH         LINE#         295
  C:082BH         LINE#         296
  C:0836H         LINE#         298
  C:083DH         LINE#         299
  C:083DH         LINE#         301
  C:0855H         LINE#         302
  C:0855H         LINE#         303
  C:0855H         LINE#         304
  C:085AH         LINE#         305
  C:085AH         LINE#         306
  C:085CH         LINE#         307
  C:085CH         LINE#         308
  C:0861H         LINE#         309
  C:0861H         LINE#         310
  C:0863H         LINE#         311
  C:0863H         LINE#         312
  C:0868H         LINE#         313
  C:0868H         LINE#         314
  C:086AH         LINE#         315
  C:086AH         LINE#         316
  C:086FH         LINE#         317
  C:086FH         LINE#         318
  C:0871H         LINE#         319
  C:0871H         LINE#         320
  C:0876H         LINE#         321
  C:0876H         LINE#         322
  C:0878H         LINE#         323
  C:0878H         LINE#         324
  C:087EH         LINE#         325
  C:0882H         LINE#         326
  C:0882H         LINE#         327
  C:0882H         LINE#         328
  C:0882H         LINE#         329
  C:0882H         LINE#         332
  C:0884H         LINE#         333
  C:0886H         LINE#         335
  C:0886H         LINE#         337
  C:088BH         LINE#         338
  C:088BH         LINE#         341
  C:0890H         LINE#         342
  C:0894H         LINE#         343
  C:0898H         LINE#         344
  C:0898H         LINE#         345
  -------         ENDPROC       COMMDEALRESPONSE
  -------         ENDMOD        COMMUNICATION

  -------         MODULE        LEDDRIVER
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 20


  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00EAH         PUBLIC        P1M0
  D:00E2H         PUBLIC        P1M1
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:0090H.2       PUBLIC        P1_2
  B:0090H.3       PUBLIC        P1_3
  B:0090H.4       PUBLIC        P1_4
  B:0090H.5       PUBLIC        P1_5
  B:0090H.6       PUBLIC        P1_6
  B:0090H.7       PUBLIC        P1_7
  C:0B08H         PUBLIC        LED1_WorkOff
  C:0B1AH         PUBLIC        LED2_WorkOff
  C:0B2CH         PUBLIC        LED3_WorkOff
  C:0B3EH         PUBLIC        LED4_WorkOff
  C:0B50H         PUBLIC        LED5_WorkOff
  C:0B62H         PUBLIC        LED6_WorkOff
  C:0AFFH         PUBLIC        LED1_WorkOn
  C:0B11H         PUBLIC        LED2_WorkOn
  C:0B23H         PUBLIC        LED3_WorkOn
  C:0B35H         PUBLIC        LED4_WorkOn
  C:0B47H         PUBLIC        LED5_WorkOn
  C:0B59H         PUBLIC        LED6_WorkOn
  C:0B8EH         PUBLIC        InitAllLedDriverRam
  C:0ABAH         PUBLIC        InitAllLedOff
  -------         PROC          INITALLLEDDRIVERRAM
  C:0B8EH         LINE#         49
  C:0B8EH         LINE#         50
  C:0B8EH         LINE#         51
  C:0B93H         LINE#         65
  -------         ENDPROC       INITALLLEDDRIVERRAM
  -------         PROC          INITALLLEDOFF
  C:0ABAH         LINE#         72
  C:0ABAH         LINE#         73
  C:0ABAH         LINE#         74
  C:0ABDH         LINE#         75
  C:0AC0H         LINE#         76
  C:0AC3H         LINE#         77
  C:0AC6H         LINE#         78
  C:0AC9H         LINE#         79
  -------         ENDPROC       INITALLLEDOFF
  -------         PROC          LED1_WORKON
  C:0AFFH         LINE#         88
  C:0AFFH         LINE#         89
  C:0AFFH         LINE#         90
  C:0B05H         LINE#         91
  C:0B07H         LINE#         92
  -------         ENDPROC       LED1_WORKON
  -------         PROC          LED1_WORKOFF
  C:0B08H         LINE#         99
  C:0B08H         LINE#         100
  C:0B08H         LINE#         101
  C:0B0EH         LINE#         102
  C:0B10H         LINE#         103
  -------         ENDPROC       LED1_WORKOFF
  -------         PROC          LED2_WORKON
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 21


  C:0B11H         LINE#         131
  C:0B11H         LINE#         132
  C:0B11H         LINE#         133
  C:0B17H         LINE#         134
  C:0B19H         LINE#         135
  -------         ENDPROC       LED2_WORKON
  -------         PROC          LED2_WORKOFF
  C:0B1AH         LINE#         142
  C:0B1AH         LINE#         143
  C:0B1AH         LINE#         144
  C:0B20H         LINE#         145
  C:0B22H         LINE#         146
  -------         ENDPROC       LED2_WORKOFF
  -------         PROC          LED3_WORKON
  C:0B23H         LINE#         175
  C:0B23H         LINE#         176
  C:0B23H         LINE#         177
  C:0B29H         LINE#         178
  C:0B2BH         LINE#         179
  -------         ENDPROC       LED3_WORKON
  -------         PROC          LED3_WORKOFF
  C:0B2CH         LINE#         186
  C:0B2CH         LINE#         187
  C:0B2CH         LINE#         188
  C:0B32H         LINE#         189
  C:0B34H         LINE#         190
  -------         ENDPROC       LED3_WORKOFF
  -------         PROC          LED4_WORKON
  C:0B35H         LINE#         218
  C:0B35H         LINE#         219
  C:0B35H         LINE#         220
  C:0B3BH         LINE#         221
  C:0B3DH         LINE#         222
  -------         ENDPROC       LED4_WORKON
  -------         PROC          LED4_WORKOFF
  C:0B3EH         LINE#         229
  C:0B3EH         LINE#         230
  C:0B3EH         LINE#         231
  C:0B44H         LINE#         232
  C:0B46H         LINE#         233
  -------         ENDPROC       LED4_WORKOFF
  -------         PROC          LED5_WORKON
  C:0B47H         LINE#         262
  C:0B47H         LINE#         263
  C:0B47H         LINE#         264
  C:0B4DH         LINE#         265
  C:0B4FH         LINE#         266
  -------         ENDPROC       LED5_WORKON
  -------         PROC          LED5_WORKOFF
  C:0B50H         LINE#         273
  C:0B50H         LINE#         274
  C:0B50H         LINE#         275
  C:0B56H         LINE#         276
  C:0B58H         LINE#         277
  -------         ENDPROC       LED5_WORKOFF
  -------         PROC          LED6_WORKON
  C:0B59H         LINE#         305
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 22


  C:0B59H         LINE#         306
  C:0B59H         LINE#         307
  C:0B5FH         LINE#         308
  C:0B61H         LINE#         309
  -------         ENDPROC       LED6_WORKON
  -------         PROC          LED6_WORKOFF
  C:0B62H         LINE#         316
  C:0B62H         LINE#         317
  C:0B62H         LINE#         318
  C:0B68H         LINE#         319
  C:0B6AH         LINE#         320
  -------         ENDPROC       LED6_WORKOFF
  -------         ENDMOD        LEDDRIVER

  -------         MODULE        KEYDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00E4H         PUBLIC        P3M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:004CH         PUBLIC        KeyDrvStr
  B:00B0H.3       PUBLIC        P3_3
  B:00B0H.7       PUBLIC        P3_7
  C:01C6H         PUBLIC        KeyScan
  C:0899H         PUBLIC        InitKeyScanRam
  C:0AE9H         SYMBOL        Com002D
  C:0AE9H         SYMBOL        L?0046
  -------         PROC          COM002D
  -------         ENDPROC       COM002D
  -------         PROC          INITKEYSCANRAM
  -------         DO            
  D:0007H         SYMBOL        i
  -------         ENDDO         
  C:0899H         LINE#         48
  C:0899H         LINE#         49
  C:0899H         LINE#         52
  C:089EH         LINE#         53
  C:08A9H         LINE#         54
  C:08A9H         LINE#         55
  C:08B5H         LINE#         56
  C:08C1H         LINE#         57
  C:08CDH         LINE#         58
  C:08D9H         LINE#         59
  C:08E5H         LINE#         60
  C:08F1H         LINE#         61
  C:08F4H         LINE#         62
  -------         ENDPROC       INITKEYSCANRAM
  -------         PROC          KEYSCAN
  -------         DO            
  X:0059H         SYMBOL        tempbuff
  -------         ENDDO         
  C:01C6H         LINE#         69
  C:01C6H         LINE#         70
  C:01C6H         LINE#         73
  C:01D1H         LINE#         74
  C:01D1H         LINE#         75
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 23


  C:01D2H         LINE#         77
  C:01D8H         LINE#         78
  C:01DEH         LINE#         79
  C:01E0H         LINE#         80
  C:01E2H         LINE#         81
  C:01E3H         LINE#         82
  C:01E4H         LINE#         83
  C:01E5H         LINE#         84
  C:01E6H         LINE#         85
  C:01E7H         LINE#         86
  C:01E8H         LINE#         87
  C:01E9H         LINE#         88
  C:01EAH         LINE#         89
  C:01EBH         LINE#         90
  C:01ECH         LINE#         91
  C:01EFH         LINE#         92
  C:01EFH         LINE#         93
  C:01F4H         LINE#         94
  C:01F6H         LINE#         96
  C:01F6H         LINE#         97
  C:01FBH         LINE#         98
  C:01FBH         LINE#         99
  C:01FEH         LINE#         100
  C:01FEH         LINE#         101
  C:0204H         LINE#         102
  C:0206H         LINE#         104
  C:0206H         LINE#         105
  C:020BH         LINE#         106
  C:020BH         LINE#         110
  C:0217H         LINE#         111
  C:0217H         LINE#         112
  C:0219H         LINE#         113
  C:021EH         LINE#         114
  C:0220H         LINE#         116
  C:0220H         LINE#         117
  C:0223H         LINE#         118
  C:0228H         LINE#         119
  C:0228H         LINE#         120
  C:022AH         LINE#         122
  C:0237H         LINE#         123
  C:0237H         LINE#         124
  C:023CH         LINE#         126
  C:023FH         LINE#         127
  C:023FH         LINE#         128
  C:0245H         LINE#         129
  C:0247H         LINE#         130
  C:024EH         LINE#         131
  C:024EH         LINE#         132
  C:0251H         LINE#         133
  C:0257H         LINE#         134
  C:025AH         LINE#         135
  C:025CH         LINE#         137
  C:025CH         LINE#         138
  C:025FH         LINE#         139
  C:025FH         LINE#         140
  C:0261H         LINE#         142
  C:0261H         LINE#         143
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 24


  C:0265H         LINE#         144
  C:0265H         LINE#         145
  C:026BH         LINE#         146
  C:026DH         LINE#         147
  C:0270H         LINE#         148
  C:0270H         LINE#         149
  C:0276H         LINE#         150
  C:0278H         LINE#         152
  C:0278H         LINE#         153
  C:027DH         LINE#         154
  C:0281H         LINE#         155
  C:0285H         LINE#         156
  C:0289H         LINE#         157
  C:0289H         LINE#         158
  C:0289H         LINE#         159
  C:0289H         LINE#         160
  C:0289H         LINE#         163
  C:0295H         LINE#         164
  C:0295H         LINE#         165
  C:0297H         LINE#         166
  C:029CH         LINE#         167
  C:029DH         LINE#         169
  C:029DH         LINE#         170
  C:02A0H         LINE#         171
  C:02A5H         LINE#         172
  C:02A5H         LINE#         173
  C:02A7H         LINE#         175
  C:02B4H         LINE#         176
  C:02B4H         LINE#         177
  C:02B9H         LINE#         179
  C:02BCH         LINE#         180
  C:02BCH         LINE#         181
  C:02C2H         LINE#         182
  C:02C3H         LINE#         183
  C:02CAH         LINE#         184
  C:02CAH         LINE#         185
  C:02CDH         LINE#         186
  C:02D3H         LINE#         187
  C:02D6H         LINE#         188
  C:02D8H         LINE#         190
  C:02D8H         LINE#         191
  C:02DBH         LINE#         192
  C:02DBH         LINE#         193
  C:02DCH         LINE#         195
  C:02DCH         LINE#         196
  C:02E0H         LINE#         197
  C:02E0H         LINE#         198
  C:02E6H         LINE#         199
  C:02E7H         LINE#         200
  C:02EAH         LINE#         201
  C:02EAH         LINE#         202
  C:02F0H         LINE#         203
  C:02F1H         LINE#         205
  C:02F1H         LINE#         206
  C:02F6H         LINE#         207
  C:02FAH         LINE#         208
  C:02FEH         LINE#         209
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 25


  C:0302H         LINE#         210
  C:0302H         LINE#         211
  C:0302H         LINE#         212
  C:0302H         LINE#         213
  C:0302H         LINE#         214
  C:0302H         LINE#         215
  -------         ENDPROC       KEYSCAN
  -------         ENDMOD        KEYDRIVER

  -------         MODULE        UARTDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00E4H         PUBLIC        P3M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00A8H         PUBLIC        IEN0
  C:0939H         PUBLIC        _Uart0CalBaudrate
  B:0098H.0       PUBLIC        RI
  D:00CBH         PUBLIC        RCAP2H
  D:00CAH         PUBLIC        RCAP2L
  D:0087H         PUBLIC        PCON
  D:0098H         PUBLIC        SCON
  D:00CDH         PUBLIC        TH2
  D:00CCH         PUBLIC        TL2
  B:00C8H.2       PUBLIC        TR2
  C:09DFH         PUBLIC        Uart0Init
  B:0098H.4       PUBLIC        REN
  D:00C9H         PUBLIC        T2MOD
  D:00C8H         PUBLIC        T2CON
  D:009BH         PUBLIC        SADEN
  D:009AH         PUBLIC        SADDR
  -------         PROC          UART0INIT
  C:09DFH         LINE#         49
  C:09DFH         LINE#         50
  C:09DFH         LINE#         51
  C:09E2H         LINE#         52
  C:09E5H         LINE#         53
  C:09E8H         LINE#         59
  C:09EBH         LINE#         60
  C:09EEH         LINE#         61
  C:09F1H         LINE#         63
  C:09F4H         LINE#         64
  C:09F6H         LINE#         66
  C:09FDH         LINE#         67
  C:0A00H         LINE#         68
  C:0A02H         LINE#         69
  C:0A04H         LINE#         70
  C:0A07H         LINE#         71
  -------         ENDPROC       UART0INIT
  -------         PROC          _UART0CALBAUDRATE
  D:0006H         SYMBOL        baudratepar
  -------         DO            
  X:0059H         SYMBOL        CalBaudRateTemp
  -------         ENDDO         
  C:0939H         LINE#         80
  C:0939H         LINE#         81
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 26


  C:0939H         LINE#         84
  C:0957H         LINE#         89
  C:095AH         LINE#         90
  C:095DH         LINE#         91
  C:0960H         LINE#         92
  C:0963H         LINE#         93
  C:0966H         LINE#         94
  C:0969H         LINE#         95
  C:096CH         LINE#         96
  C:096EH         LINE#         97
  C:0970H         LINE#         99
  C:0973H         LINE#         100
  C:0976H         LINE#         101
  C:0978H         LINE#         102
  -------         ENDPROC       _UART0CALBAUDRATE
  -------         ENDMOD        UARTDRIVER

  -------         MODULE        PWMDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  C:0AA7H         PUBLIC        Timer1_Init
  D:0089H         PUBLIC        TMOD
  B:00A8H.3       PUBLIC        ET1
  D:008DH         PUBLIC        TH1
  D:008BH         PUBLIC        TL1
  B:0088H.6       PUBLIC        TR1
  -------         PROC          TIMER1_INIT
  C:0AA7H         LINE#         9
  C:0AA7H         LINE#         10
  C:0AAAH         LINE#         11
  C:0AADH         LINE#         12
  C:0AB0H         LINE#         13
  C:0AB3H         LINE#         14
  C:0AB5H         LINE#         15
  C:0AB7H         LINE#         16
  C:0AB9H         LINE#         17
  -------         ENDPROC       TIMER1_INIT
  -------         ENDMOD        PWMDRIVER

  -------         MODULE        ?C?CLDOPTR
  C:0076H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?UIDIV
  C:00A3H         PUBLIC        ?C?UIDIV
  -------         ENDMOD        ?C?UIDIV

  -------         MODULE        ?C?SLDIV
  C:08F5H         PUBLIC        ?C?SLDIV
  -------         ENDMOD        ?C?SLDIV

  -------         MODULE        ?C?ULDIV
  C:0134H         PUBLIC        ?C?ULDIV
  -------         ENDMOD        ?C?ULDIV
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:27:51  PAGE 27



Program Size: data=9.0 xdata=99 code=2963
LINK/LOCATE RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
