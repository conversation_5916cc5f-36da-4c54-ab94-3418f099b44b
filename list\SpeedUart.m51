BL51 BANKED LINKER/LOCATER V6.22.2.0                                                    06/24/2025  18:11:12  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22.2.0, INVOKED BY:
D:\KEIL_V5\C51\BIN\BL51.EXE .\output\STARTUP.obj, .\output\Main.obj, .\output\Mcu.obj, .\output\Interrupt.obj, .\output\
>> LedApp.obj, .\output\KeyApp.obj, .\output\Communication.obj, .\output\LedDriver.obj, .\output\KeyDriver.obj, .\output
>> \UartDriver.obj, .\output\PwmDriver.obj TO .\output\SpeedUart PRINT (.\list\SpeedUart.m51) RAMSIZE (256)


MEMORY MODEL: LARGE


INPUT MODULES INCLUDED:
  .\output\STARTUP.obj (?C_STARTUP)
  .\output\Main.obj (MAIN)
  .\output\Mcu.obj (MCU)
  .\output\Interrupt.obj (INTERRUPT)
  .\output\LedApp.obj (LEDAPP)
  .\output\KeyApp.obj (KEYAPP)
  .\output\Communication.obj (COMMUNICATION)
  .\output\LedDriver.obj (LEDDRIVER)
  .\output\KeyDriver.obj (KEYDRIVER)
  .\output\UartDriver.obj (UARTDRIVER)
  .\output\PwmDriver.obj (PWMDRIVER)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C_INIT)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?CLDOPTR)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?UIDIV)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?SLDIV)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?ULDIV)


LINK MAP OF MODULE:  .\output\SpeedUart (?C_STARTUP)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            IDATA   0008H     0001H     UNIT         ?STACK

            * * * * * * *  X D A T A   M E M O R Y  * * * * * * *
            XDATA   0000H     003BH     UNIT         ?XD?COMMUNICATION
            XDATA   003BH     0011H     UNIT         ?XD?INTERRUPT
            XDATA   004CH     000DH     UNIT         ?XD?KEYDRIVER
            XDATA   0059H     0005H     UNIT         _XDATA_GROUP_
            XDATA   005EH     0003H     UNIT         ?XD?LEDAPP
            XDATA   0061H     0002H     UNIT         ?XD?KEYAPP

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0003H     ABSOLUTE     
            CODE    0006H     0005H     UNIT         ?PR?EX0_INT?INTERRUPT
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0005H     UNIT         ?PR?EX1_INT?INTERRUPT
            CODE    0013H     0003H     ABSOLUTE     
            CODE    0016H     0005H     UNIT         ?PR?TIMER2_INT?INTERRUPT
            CODE    001BH     0003H     ABSOLUTE     
            CODE    001EH     0004H     UNIT         ?PR?SCM_INT?INTERRUPT
                    0022H     0001H                  *** GAP ***
            CODE    0023H     0003H     ABSOLUTE     
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 2


            CODE    0026H     0004H     UNIT         ?PR?PWM_INT?INTERRUPT
                    002AH     0001H                  *** GAP ***
            CODE    002BH     0003H     ABSOLUTE     
            CODE    002EH     001AH     UNIT         ?PR?INITT0?MCU
            CODE    0048H     0003H     UNIT         ?PR?INITCPUACSET?MCU
            CODE    004BH     0003H     ABSOLUTE     
            CODE    004EH     000DH     UNIT         ?PR?MONITORCPUTIMER?MCU
            CODE    005BH     0003H     ABSOLUTE     
            CODE    005EH     0004H     UNIT         ?PR?ELPD_INT?INTERRUPT
                    0062H     0001H                  *** GAP ***
            CODE    0063H     0003H     ABSOLUTE     
            CODE    0066H     000CH     UNIT         ?PR?INITINTERRUPTRAM?INTERRUPT
                    0072H     0001H                  *** GAP ***
            CODE    0073H     0003H     ABSOLUTE     
            CODE    0076H     0150H     UNIT         ?C?LIB_CODE
            CODE    01C6H     013DH     UNIT         ?PR?KEYSCAN?KEYDRIVER
            CODE    0303H     00D0H     UNIT         ?PR?EUART0_INT?INTERRUPT
            CODE    03D3H     00C8H     UNIT         ?PR?MAIN?MAIN
            CODE    049BH     00BFH     UNIT         ?PR?_TXDDATAPROC?COMMUNICATION
            CODE    055AH     00BCH     UNIT         ?PR?TIMER0_INT?INTERRUPT
            CODE    0616H     008CH     UNIT         ?C_C51STARTUP
            CODE    06A2H     008BH     UNIT         ?PR?KEYPROC?KEYAPP
            CODE    072DH     0076H     UNIT         ?PR?LEDAPP?LEDAPP
            CODE    07A3H     0076H     UNIT         ?PR?COMMDEALRESPONSE?COMMUNICATION
            CODE    0819H     006DH     UNIT         ?PR?TIMER1_INT?INTERRUPT
            CODE    0886H     005CH     UNIT         ?PR?INITKEYSCANRAM?KEYDRIVER
            CODE    08E2H     0044H     UNIT         ?C?LDIV
            CODE    0926H     0040H     UNIT         ?PR?_UART0CALBAUDRATE?UARTDRIVER
            CODE    0966H     003AH     UNIT         ?PR?INTERRUPT
            CODE    09A0H     002CH     UNIT         ?PR?_CALCCHECKSUM?COMMUNICATION
            CODE    09CCH     0029H     UNIT         ?PR?UART0INIT?UARTDRIVER
            CODE    09F5H     0022H     UNIT         ?PR?KEYAPP
            CODE    0A17H     0021H     UNIT         ?PR?_DELAYNMS?MCU
            CODE    0A38H     001AH     UNIT         ?PR?COMMFUNPROC?COMMUNICATION
            CODE    0A52H     0019H     UNIT         ?PR?INITPORT?MCU
            CODE    0A6BH     0015H     UNIT         ?C_INITSEG
            CODE    0A80H     0014H     UNIT         ?PR?INITT1?MCU
            CODE    0A94H     0013H     UNIT         ?PR?TIMER1_INIT?PWMDRIVER
            CODE    0AA7H     0012H     UNIT         ?PR?INITALLLEDOFF?LEDDRIVER
            CODE    0AB9H     000FH     UNIT         ?PR?COMMUNICATION
            CODE    0AC8H     000EH     UNIT         ?PR?INITSYS?MCU
            CODE    0AD6H     000CH     UNIT         ?PR?KEYDRIVER
            CODE    0AE2H     000AH     UNIT         ?PR?INITINTERRUPTPRIORITYLEVEL?MCU
            CODE    0AECH     0009H     UNIT         ?PR?LED1_WORKON?LEDDRIVER
            CODE    0AF5H     0009H     UNIT         ?PR?LED1_WORKOFF?LEDDRIVER
            CODE    0AFEH     0009H     UNIT         ?PR?LED2_WORKON?LEDDRIVER
            CODE    0B07H     0009H     UNIT         ?PR?LED2_WORKOFF?LEDDRIVER
            CODE    0B10H     0009H     UNIT         ?PR?LED3_WORKON?LEDDRIVER
            CODE    0B19H     0009H     UNIT         ?PR?LED3_WORKOFF?LEDDRIVER
            CODE    0B22H     0009H     UNIT         ?PR?LED4_WORKON?LEDDRIVER
            CODE    0B2BH     0009H     UNIT         ?PR?LED4_WORKOFF?LEDDRIVER
            CODE    0B34H     0009H     UNIT         ?PR?LED5_WORKON?LEDDRIVER
            CODE    0B3DH     0009H     UNIT         ?PR?LED5_WORKOFF?LEDDRIVER
            CODE    0B46H     0009H     UNIT         ?PR?LED6_WORKON?LEDDRIVER
            CODE    0B4FH     0009H     UNIT         ?PR?LED6_WORKOFF?LEDDRIVER
            CODE    0B58H     0008H     UNIT         ?PR?INITPWM?MCU
            CODE    0B60H     0007H     UNIT         ?PR?INITINT1?MCU
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 3


            CODE    0B67H     0007H     UNIT         ?PR?INITINT2?MCU
            CODE    0B6EH     0007H     UNIT         ?PR?EX2_INT?INTERRUPT
            CODE    0B75H     0006H     UNIT         ?PR?INITKEYPROCRAM?KEYAPP
            CODE    0B7BH     0006H     UNIT         ?PR?INITALLLEDDRIVERRAM?LEDDRIVER
            CODE    0B81H     0003H     UNIT         ?PR?INITALLLEDRAMAPP?LEDAPP



OVERLAY MAP OF MODULE:   .\output\SpeedUart (?C_STARTUP)


SEGMENT                                      XDATA_GROUP
  +--> CALLED SEGMENT                      START    LENGTH
----------------------------------------------------------
?C_C51STARTUP                              -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                              -----    -----
  +--> ?PR?INITSYS?MCU
  +--> ?PR?_DELAYNMS?MCU
  +--> ?PR?INITPORT?MCU
  +--> ?PR?INITKEYSCANRAM?KEYDRIVER
  +--> ?PR?INITKEYPROCRAM?KEYAPP
  +--> ?PR?INITINTERRUPTRAM?INTERRUPT
  +--> ?PR?INITALLLEDOFF?LEDDRIVER
  +--> ?PR?INITALLLEDRAMAPP?LEDAPP
  +--> ?PR?INITT0?MCU
  +--> ?PR?INITT1?MCU
  +--> ?PR?INITINT1?MCU
  +--> ?PR?INITINT2?MCU
  +--> ?PR?INITPWM?MCU
  +--> ?PR?UART0INIT?UARTDRIVER
  +--> ?PR?MONITORCPUTIMER?MCU
  +--> ?PR?INITCPUACSET?MCU
  +--> ?PR?KEYSCAN?KEYDRIVER
  +--> ?PR?KEYPROC?KEYAPP
  +--> ?PR?LEDAPP?LEDAPP
  +--> ?PR?COMMFUNPROC?COMMUNICATION
  +--> ?PR?TIMER1_INIT?PWMDRIVER

?PR?INITSYS?MCU                            -----    -----
  +--> ?PR?INITINTERRUPTPRIORITYLEVEL?MCU

?PR?INITINTERRUPTRAM?INTERRUPT             -----    -----
  +--> ?PR?INTERRUPT

?PR?INITALLLEDOFF?LEDDRIVER                -----    -----
  +--> ?PR?LED1_WORKOFF?LEDDRIVER
  +--> ?PR?LED2_WORKOFF?LEDDRIVER
  +--> ?PR?LED3_WORKOFF?LEDDRIVER
  +--> ?PR?LED4_WORKOFF?LEDDRIVER
  +--> ?PR?LED5_WORKOFF?LEDDRIVER
  +--> ?PR?LED6_WORKOFF?LEDDRIVER

?PR?INITALLLEDRAMAPP?LEDAPP                -----    -----
  +--> ?PR?INITALLLEDDRIVERRAM?LEDDRIVER
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 4



?PR?UART0INIT?UARTDRIVER                   -----    -----
  +--> ?PR?_UART0CALBAUDRATE?UARTDRIVER

?PR?_UART0CALBAUDRATE?UARTDRIVER           0059H    0002H

?PR?KEYSCAN?KEYDRIVER                      0059H    0002H
  +--> ?PR?KEYDRIVER

?PR?KEYPROC?KEYAPP                         -----    -----
  +--> ?PR?KEYAPP
  +--> ?PR?_TXDDATAPROC?COMMUNICATION

?PR?_TXDDATAPROC?COMMUNICATION             0059H    0001H
  +--> ?PR?COMMUNICATION
  +--> ?PR?_CALCCHECKSUM?COMMUNICATION

?PR?_CALCCHECKSUM?COMMUNICATION            005AH    0003H

?PR?LEDAPP?LEDAPP                          -----    -----
  +--> ?PR?INITALLLEDOFF?LEDDRIVER
  +--> ?PR?LED1_WORKON?LEDDRIVER
  +--> ?PR?LED2_WORKON?LEDDRIVER
  +--> ?PR?LED3_WORKON?LEDDRIVER
  +--> ?PR?LED4_WORKON?LEDDRIVER
  +--> ?PR?LED5_WORKON?LEDDRIVER
  +--> ?PR?LED6_WORKON?LEDDRIVER

?PR?COMMFUNPROC?COMMUNICATION              -----    -----
  +--> ?PR?_TXDDATAPROC?COMMUNICATION
  +--> ?PR?COMMDEALRESPONSE?COMMUNICATION

?PR?COMMDEALRESPONSE?COMMUNICATION         -----    -----
  +--> ?PR?_CALCCHECKSUM?COMMUNICATION
  +--> ?PR?_TXDDATAPROC?COMMUNICATION

*** NEW ROOT ***************************************************

?PR?TIMER0_INT?INTERRUPT                   -----    -----
  +--> ?PR?INTERRUPT

*** NEW ROOT ***************************************************

?PR?EUART0_INT?INTERRUPT                   005DH    0001H
  +--> ?PR?INTERRUPT



SYMBOL TABLE OF MODULE:  .\output\SpeedUart (?C_STARTUP)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        ?C_STARTUP
  C:0616H         SEGMENT       ?C_C51STARTUP
  I:0008H         SEGMENT       ?STACK
  C:0000H         PUBLIC        ?C_STARTUP
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 5


  D:00E0H         SYMBOL        ACC
  D:00F0H         SYMBOL        B
  N:3026H         SYMBOL        CODE_SIZE
  D:0083H         SYMBOL        DPH
  D:0082H         SYMBOL        DPL
  N:0F9AH         SYMBOL        FILLING_A5_NUM
  C:0622H         SYMBOL        FILL_CODE
  N:0000H         SYMBOL        IBPSTACK
  N:0100H         SYMBOL        IBPSTACKTOP
  N:0080H         SYMBOL        IDATALEN
  C:0619H         SYMBOL        IDATALOOP
  N:0000H         SYMBOL        PBPSTACK
  N:0000H         SYMBOL        PBPSTACKTOP
  N:0000H         SYMBOL        PDATALEN
  N:0000H         SYMBOL        PDATASTART
  N:0000H         SYMBOL        PPAGE
  N:0000H         SYMBOL        PPAGEENABLE
  D:00A0H         SYMBOL        PPAGE_SFR
  N:3FC0H         SYMBOL        ROM_SIZE
  D:0081H         SYMBOL        SP
  C:0616H         SYMBOL        STARTUP1
  N:0000H         SYMBOL        XBPSTACK
  N:0000H         SYMBOL        XBPSTACKTOP
  N:0000H         SYMBOL        XDATALEN
  N:0000H         SYMBOL        XDATASTART
  C:0000H         LINE#         107
  C:0616H         LINE#         114
  C:0618H         LINE#         115
  C:0619H         LINE#         116
  C:061AH         LINE#         117
  C:061CH         LINE#         166
  C:061FH         LINE#         170
  -------         ENDMOD        ?C_STARTUP

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IEN0
  D:00A9H         PUBLIC        IEN1
  C:03D3H         PUBLIC        main
  -------         PROC          MAIN
  C:03D3H         LINE#         50
  C:03D3H         LINE#         51
  C:03D3H         LINE#         52
  C:03D5H         LINE#         53
  C:03D8H         LINE#         54
  C:03DFH         LINE#         55
  C:03E2H         LINE#         56
  C:03E5H         LINE#         57
  C:03E8H         LINE#         58
  C:03EBH         LINE#         59
  C:03EEH         LINE#         60
  C:03F1H         LINE#         61
  C:03F4H         LINE#         62
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 6


  C:03F7H         LINE#         63
  C:03FAH         LINE#         64
  C:03FDH         LINE#         65
  C:0400H         LINE#         66
  C:0403H         LINE#         67
  C:0406H         LINE#         68
  C:0409H         LINE#         69
  C:040BH         LINE#         80
  C:040BH         LINE#         81
  C:040BH         LINE#         82
  C:040EH         LINE#         83
  C:0411H         LINE#         84
  C:0414H         LINE#         85
  C:0417H         LINE#         86
  C:041AH         LINE#         87
  C:041DH         LINE#         88
  C:0420H         LINE#         89
  C:0423H         LINE#         92
  C:042BH         LINE#         96
  C:042EH         LINE#         98
  C:0445H         LINE#         99
  C:0445H         LINE#         100
  C:0447H         LINE#         101
  C:0452H         LINE#         102
  C:0455H         LINE#         103
  C:0457H         LINE#         104
  C:0462H         LINE#         105
  C:0465H         LINE#         106
  C:0467H         LINE#         107
  C:0472H         LINE#         108
  C:0475H         LINE#         109
  C:0477H         LINE#         110
  C:0482H         LINE#         111
  C:0482H         LINE#         112
  C:0484H         LINE#         113
  C:048FH         LINE#         114
  C:0492H         LINE#         115
  C:0495H         LINE#         116
  C:0495H         LINE#         117
  C:0498H         LINE#         118
  C:0498H         LINE#         119
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        MCU
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00EAH         PUBLIC        P1M0
  D:00E2H         PUBLIC        P1M1
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00EDH         PUBLIC        P4M0
  D:00E4H         PUBLIC        P3M1
  D:00E5H         PUBLIC        P4M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  D:00A9H         PUBLIC        IEN1
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 7


  D:00B4H         PUBLIC        IPH0
  D:00B5H         PUBLIC        IPH1
  D:00E8H         PUBLIC        EXF0
  D:00B8H         PUBLIC        IPL0
  D:00B9H         PUBLIC        IPL1
  C:002EH         PUBLIC        InitT0
  C:004EH         PUBLIC        MonitorCpuTimer
  C:0A80H         PUBLIC        InitT1
  C:0A17H         PUBLIC        _DelayNms
  C:0B60H         PUBLIC        InitINT1
  C:0B67H         PUBLIC        InitINT2
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:00B1H         PUBLIC        RSTSTAT
  D:00D3H         PUBLIC        PWMD
  D:00B2H         PUBLIC        CLKCON
  C:0A52H         PUBLIC        InitPort
  B:0088H.3       PUBLIC        IE1
  D:00B3H         PUBLIC        LPDCON
  D:00D2H         PUBLIC        PWMP
  B:0088H.5       PUBLIC        TF0
  B:0088H.7       PUBLIC        TF1
  C:0B58H         PUBLIC        InitPwm
  D:008CH         PUBLIC        TH0
  D:008DH         PUBLIC        TH1
  B:0088H.2       PUBLIC        IT1
  B:00A8H.2       PUBLIC        EX1
  C:0AE2H         PUBLIC        InitInterruptPriorityLevel
  D:008AH         PUBLIC        TL0
  D:008BH         PUBLIC        TL1
  B:0088H.4       PUBLIC        TR0
  D:00D1H         PUBLIC        PWMCON
  C:0AC8H         PUBLIC        InitSys
  B:0088H.6       PUBLIC        TR1
  D:00CEH         PUBLIC        TCON1
  C:0048H         PUBLIC        InitCpuACSet
  -------         PROC          INITSYS
  C:0AC8H         LINE#         50
  C:0AC8H         LINE#         51
  C:0AC8H         LINE#         52
  C:0ACAH         LINE#         53
  C:0ACDH         LINE#         54
  C:0ACFH         LINE#         55
  C:0AD2H         LINE#         56
  C:0AD5H         LINE#         57
  -------         ENDPROC       INITSYS
  -------         PROC          INITCPUACSET
  C:0048H         LINE#         64
  C:0048H         LINE#         65
  C:0048H         LINE#         66
  C:004AH         LINE#         67
  -------         ENDPROC       INITCPUACSET
  -------         PROC          INITPORT
  C:0A52H         LINE#         74
  C:0A52H         LINE#         75
  C:0A52H         LINE#         76
  C:0A55H         LINE#         77
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 8


  C:0A58H         LINE#         78
  C:0A5AH         LINE#         80
  C:0A5DH         LINE#         81
  C:0A60H         LINE#         83
  C:0A62H         LINE#         84
  C:0A65H         LINE#         86
  C:0A67H         LINE#         87
  C:0A6AH         LINE#         88
  -------         ENDPROC       INITPORT
  -------         PROC          INITINTERRUPTPRIORITYLEVEL
  C:0AE2H         LINE#         96
  C:0AE2H         LINE#         97
  C:0AE2H         LINE#         98
  C:0AE5H         LINE#         99
  C:0AE7H         LINE#         100
  C:0AE9H         LINE#         101
  C:0AEBH         LINE#         102
  -------         ENDPROC       INITINTERRUPTPRIORITYLEVEL
  -------         PROC          INITT0
  C:002EH         LINE#         109
  C:002EH         LINE#         110
  C:002EH         LINE#         111
  C:0031H         LINE#         113
  C:0034H         LINE#         114
  C:0037H         LINE#         116
  C:003AH         LINE#         117
  C:003DH         LINE#         119
  C:0040H         LINE#         120
  C:0043H         LINE#         121
  C:0045H         LINE#         122
  C:0047H         LINE#         123
  -------         ENDPROC       INITT0
  -------         PROC          INITT1
  C:0A80H         LINE#         131
  C:0A80H         LINE#         132
  C:0A80H         LINE#         133
  C:0A83H         LINE#         135
  C:0A86H         LINE#         136
  C:0A86H         LINE#         139
  C:0A89H         LINE#         140
  C:0A89H         LINE#         143
  C:0A8CH         LINE#         144
  C:0A8FH         LINE#         145
  C:0A91H         LINE#         146
  C:0A93H         LINE#         147
  -------         ENDPROC       INITT1
  -------         PROC          INITINT1
  C:0B60H         LINE#         155
  C:0B60H         LINE#         156
  C:0B60H         LINE#         157
  C:0B62H         LINE#         158
  C:0B64H         LINE#         159
  C:0B66H         LINE#         160
  -------         ENDPROC       INITINT1
  -------         PROC          INITINT2
  C:0B67H         LINE#         167
  C:0B67H         LINE#         168
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 9


  C:0B67H         LINE#         169
  C:0B6AH         LINE#         170
  C:0B6DH         LINE#         171
  -------         ENDPROC       INITINT2
  -------         PROC          INITPWM
  C:0B58H         LINE#         179
  C:0B58H         LINE#         180
  C:0B58H         LINE#         185
  C:0B5BH         LINE#         186
  C:0B5DH         LINE#         187
  C:0B5FH         LINE#         188
  -------         ENDPROC       INITPWM
  -------         PROC          MONITORCPUTIMER
  C:004EH         LINE#         196
  C:004EH         LINE#         197
  C:004EH         LINE#         198
  C:0056H         LINE#         199
  C:0056H         LINE#         200
  C:0057H         LINE#         202
  C:005AH         LINE#         203
  C:005AH         LINE#         204
  -------         ENDPROC       MONITORCPUTIMER
  -------         PROC          _DELAYNMS
  D:0006H         SYMBOL        num
  -------         DO            
  D:0004H         SYMBOL        i
  D:0002H         SYMBOL        j
  -------         ENDDO         
  C:0A17H         LINE#         212
  C:0A17H         LINE#         213
  C:0A17H         LINE#         216
  C:0A21H         LINE#         217
  C:0A21H         LINE#         218
  C:0A24H         LINE#         219
  C:0A24H         LINE#         220
  C:0A25H         LINE#         221
  C:0A30H         LINE#         222
  C:0A37H         LINE#         223
  -------         ENDPROC       _DELAYNMS
  -------         ENDMOD        MCU

  -------         MODULE        INTERRUPT
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:0303H         PUBLIC        EUART0_INT
  C:055AH         PUBLIC        Timer0_INT
  D:0090H         PUBLIC        P1
  C:0819H         PUBLIC        Timer1_INT
  C:0016H         PUBLIC        Timer2_INT
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:0066H         PUBLIC        InitInterruptRam
  D:00A9H         PUBLIC        IEN1
  D:00E8H         PUBLIC        EXF0
  B:00C8H.6       PUBLIC        EXF2
  C:0006H         PUBLIC        EX0_INT
  C:000EH         PUBLIC        EX1_INT
  B:0098H.0       PUBLIC        RI
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 10


  C:0B6EH         PUBLIC        EX2_INT
  B:0098H.1       PUBLIC        TI
  X:003BH         PUBLIC        count
  X:003CH         PUBLIC        high_count
  C:001EH         PUBLIC        SCM_INT
  D:0099H         PUBLIC        SBUF
  X:003DH         PUBLIC        duty_shadow
  D:00B2H         PUBLIC        CLKCON
  B:0088H.1       PUBLIC        IE0
  B:0088H.3       PUBLIC        IE1
  C:0026H         PUBLIC        PWM_INT
  D:00B3H         PUBLIC        LPDCON
  X:003EH         PUBLIC        duty
  X:003FH         PUBLIC        TimeFlagStr
  B:0088H.5       PUBLIC        TF0
  B:0088H.7       PUBLIC        TF1
  B:00C8H.7       PUBLIC        TF2
  D:008CH         PUBLIC        TH0
  B:00A8H.0       PUBLIC        EX0
  D:008DH         PUBLIC        TH1
  B:00A8H.2       PUBLIC        EX1
  D:008AH         PUBLIC        TL0
  D:008BH         PUBLIC        TL1
  B:0088H.4       PUBLIC        TR0
  D:00D1H         PUBLIC        PWMCON
  B:0088H.6       PUBLIC        TR1
  B:0098H.4       PUBLIC        REN
  B:00B0H.0       PUBLIC        PWM_IN
  C:005EH         PUBLIC        ELPD_INT
  C:0966H         SYMBOL        Com0028
  C:0966H         SYMBOL        L?0041
  C:0972H         SYMBOL        L?0042
  C:0980H         SYMBOL        L?0043
  C:0988H         SYMBOL        L?0044
  C:098BH         SYMBOL        L?0045
  C:0995H         SYMBOL        L?0046
  -------         PROC          COM0028
  -------         ENDPROC       COM0028
  -------         PROC          INITINTERRUPTRAM
  C:0066H         LINE#         52
  C:0066H         LINE#         53
  C:0066H         LINE#         54
  C:006AH         LINE#         56
  C:006AH         LINE#         57
  C:006AH         LINE#         58
  C:006AH         LINE#         60
  C:006AH         LINE#         61
  C:006DH         LINE#         63
  C:006EH         LINE#         64
  C:006EH         LINE#         66
  C:006EH         LINE#         67
  C:0071H         LINE#         68
  -------         ENDPROC       INITINTERRUPTRAM
  -------         PROC          EX0_INT
  C:0006H         LINE#         75
  C:0006H         LINE#         77
  C:0008H         LINE#         78
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 11


  C:000AH         LINE#         79
  -------         ENDPROC       EX0_INT
  -------         PROC          TIMER0_INT
  C:055AH         LINE#         86
  C:0562H         LINE#         88
  C:0564H         LINE#         89
  C:0566H         LINE#         90
  C:0569H         LINE#         91
  C:056CH         LINE#         92
  C:056EH         LINE#         98
  C:0574H         LINE#         100
  C:057AH         LINE#         101
  C:0587H         LINE#         102
  C:0587H         LINE#         103
  C:0589H         LINE#         105
  C:058AH         LINE#         106
  C:058FH         LINE#         107
  C:058FH         LINE#         108
  C:0591H         LINE#         109
  C:0591H         LINE#         111
  C:0597H         LINE#         112
  C:05A1H         LINE#         113
  C:05A1H         LINE#         114
  C:05A3H         LINE#         115
  C:05A6H         LINE#         117
  C:05AAH         LINE#         119
  C:05ADH         LINE#         120
  C:05B2H         LINE#         121
  C:05B2H         LINE#         122
  C:05B4H         LINE#         123
  C:05B7H         LINE#         125
  C:05BDH         LINE#         126
  C:05C0H         LINE#         127
  C:05C0H         LINE#         128
  C:05C3H         LINE#         129
  C:05C5H         LINE#         130
  C:05C5H         LINE#         133
  C:05D3H         LINE#         134
  C:05E2H         LINE#         135
  C:05E2H         LINE#         136
  C:05E6H         LINE#         137
  C:05E9H         LINE#         139
  C:05F7H         LINE#         140
  C:0606H         LINE#         141
  C:0606H         LINE#         142
  C:060AH         LINE#         143
  C:060DH         LINE#         144
  C:060DH         LINE#         145
  C:060DH         LINE#         146
  C:060DH         LINE#         171
  C:060DH         LINE#         172
  -------         ENDPROC       TIMER0_INT
  -------         PROC          EX1_INT
  C:000EH         LINE#         179
  C:000EH         LINE#         181
  C:0010H         LINE#         182
  C:0012H         LINE#         183
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 12


  -------         ENDPROC       EX1_INT
  -------         PROC          TIMER1_INT
  C:0819H         LINE#         190
  C:0830H         LINE#         192
  C:0832H         LINE#         193
  C:0834H         LINE#         194
  C:0837H         LINE#         195
  C:083AH         LINE#         196
  C:083CH         LINE#         198
  C:0842H         LINE#         199
  C:0849H         LINE#         201
  C:0857H         LINE#         202
  C:086AH         LINE#         205
  C:086FH         LINE#         206
  C:0871H         LINE#         207
  C:0871H         LINE#         208
  -------         ENDPROC       TIMER1_INT
  -------         PROC          EUART0_INT
  -------         DO            
  X:005DH         SYMBOL        TempSBUF
  -------         ENDDO         
  C:0303H         LINE#         304
  C:0312H         LINE#         308
  C:0318H         LINE#         309
  C:0318H         LINE#         310
  C:031AH         LINE#         312
  C:0320H         LINE#         313
  C:0326H         LINE#         315
  C:032DH         LINE#         316
  C:032DH         LINE#         317
  C:0336H         LINE#         318
  C:0336H         LINE#         319
  C:0336H         LINE#         320
  C:0344H         LINE#         321
  C:0347H         LINE#         322
  C:034DH         LINE#         324
  C:0350H         LINE#         326
  C:0350H         LINE#         331
  C:0350H         LINE#         332
  C:0350H         LINE#         333
  C:0355H         LINE#         334
  C:0355H         LINE#         335
  C:035EH         LINE#         336
  C:035EH         LINE#         337
  C:035EH         LINE#         338
  C:035EH         LINE#         339
  C:0361H         LINE#         340
  C:0367H         LINE#         342
  C:0369H         LINE#         344
  C:0369H         LINE#         345
  C:036EH         LINE#         346
  C:0372H         LINE#         347
  C:0376H         LINE#         348
  C:0378H         LINE#         349
  C:0378H         LINE#         350
  C:0378H         LINE#         351
  C:0380H         LINE#         352
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 13


  C:0380H         LINE#         353
  C:0385H         LINE#         354
  C:0388H         LINE#         356
  C:0393H         LINE#         357
  C:0393H         LINE#         358
  C:0396H         LINE#         359
  C:0396H         LINE#         360
  C:0399H         LINE#         362
  C:039BH         LINE#         363
  C:039BH         LINE#         364
  C:039DH         LINE#         366
  C:039DH         LINE#         367
  C:039DH         LINE#         368
  C:03A0H         LINE#         369
  C:03A2H         LINE#         370
  C:03A2H         LINE#         371
  C:03A2H         LINE#         373
  C:03A5H         LINE#         374
  C:03A5H         LINE#         375
  C:03A7H         LINE#         377
  C:03B2H         LINE#         378
  C:03B2H         LINE#         379
  C:03B5H         LINE#         380
  C:03C2H         LINE#         381
  C:03C4H         LINE#         383
  C:03C4H         LINE#         384
  C:03C6H         LINE#         385
  C:03C6H         LINE#         386
  C:03C6H         LINE#         387
  -------         ENDPROC       EUART0_INT
  -------         PROC          TIMER2_INT
  C:0016H         LINE#         492
  C:0016H         LINE#         494
  C:0018H         LINE#         495
  C:001AH         LINE#         496
  -------         ENDPROC       TIMER2_INT
  -------         PROC          EX2_INT
  C:0B6EH         LINE#         503
  C:0B6EH         LINE#         505
  C:0B71H         LINE#         506
  C:0B74H         LINE#         507
  -------         ENDPROC       EX2_INT
  -------         PROC          SCM_INT
  C:001EH         LINE#         514
  C:001EH         LINE#         516
  C:0021H         LINE#         517
  -------         ENDPROC       SCM_INT
  -------         PROC          PWM_INT
  C:0026H         LINE#         524
  C:0026H         LINE#         526
  C:0029H         LINE#         527
  -------         ENDPROC       PWM_INT
  -------         PROC          ELPD_INT
  C:005EH         LINE#         534
  C:005EH         LINE#         536
  C:0061H         LINE#         537
  -------         ENDPROC       ELPD_INT
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 14


  -------         ENDMOD        INTERRUPT

  -------         MODULE        LEDAPP
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:005EH         PUBLIC        LedWorkGroupStr
  C:0B81H         PUBLIC        InitAllLedRamApp
  C:072DH         PUBLIC        LedApp
  -------         PROC          INITALLLEDRAMAPP
  C:0B81H         LINE#         50
  C:0B81H         LINE#         51
  C:0B81H         LINE#         52
  -------         ENDPROC       INITALLLEDRAMAPP
  -------         PROC          LEDAPP
  C:072DH         LINE#         60
  C:072DH         LINE#         61
  C:072DH         LINE#         62
  C:0735H         LINE#         63
  C:0735H         LINE#         64
  C:0736H         LINE#         66
  C:074EH         LINE#         67
  C:074EH         LINE#         68
  C:074EH         LINE#         69
  C:0751H         LINE#         70
  C:0754H         LINE#         71
  C:0754H         LINE#         73
  C:0754H         LINE#         74
  C:0757H         LINE#         75
  C:075AH         LINE#         76
  C:075DH         LINE#         77
  C:075DH         LINE#         79
  C:075DH         LINE#         80
  C:0760H         LINE#         81
  C:0763H         LINE#         82
  C:0766H         LINE#         83
  C:0769H         LINE#         84
  C:0769H         LINE#         86
  C:0769H         LINE#         87
  C:076CH         LINE#         88
  C:076FH         LINE#         89
  C:0772H         LINE#         90
  C:0775H         LINE#         91
  C:0778H         LINE#         92
  C:0778H         LINE#         94
  C:0778H         LINE#         95
  C:077BH         LINE#         96
  C:077EH         LINE#         97
  C:0781H         LINE#         98
  C:0784H         LINE#         99
  C:0787H         LINE#         100
  C:078AH         LINE#         101
  C:078AH         LINE#         103
  C:078AH         LINE#         104
  C:078DH         LINE#         105
  C:0790H         LINE#         106
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 15


  C:0793H         LINE#         107
  C:0796H         LINE#         108
  C:0799H         LINE#         109
  C:079CH         LINE#         110
  C:079FH         LINE#         111
  C:079FH         LINE#         113
  C:079FH         LINE#         114
  C:07A2H         LINE#         115
  C:07A2H         LINE#         116
  C:07A2H         LINE#         117
  C:07A2H         LINE#         118
  -------         ENDPROC       LEDAPP
  -------         ENDMOD        LEDAPP

  -------         MODULE        KEYAPP
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:06A2H         PUBLIC        KeyProc
  B:0098H.4       PUBLIC        REN
  X:0061H         PUBLIC        g_last_speed_level
  C:0B75H         PUBLIC        InitKeyProcRam
  X:0062H         PUBLIC        KeyProcStr
  C:09F5H         SYMBOL        Com000F
  C:09F5H         SYMBOL        L?0016
  -------         PROC          COM000F
  -------         ENDPROC       COM000F
  -------         PROC          INITKEYPROCRAM
  C:0B75H         LINE#         40
  C:0B75H         LINE#         41
  C:0B75H         LINE#         42
  C:0B7AH         LINE#         43
  -------         ENDPROC       INITKEYPROCRAM
  C:0721H         SYMBOL        L?0017
  -------         PROC          KEYPROC
  C:06A2H         LINE#         50
  C:06A2H         LINE#         51
  C:06A2H         LINE#         52
  C:06AAH         LINE#         53
  C:06AAH         LINE#         54
  C:06ABH         LINE#         56
  C:06B3H         LINE#         57
  C:06B3H         LINE#         58
  C:06B4H         LINE#         59
  C:06BCH         LINE#         60
  C:06BCH         LINE#         61
  C:06BDH         LINE#         64
  C:06C3H         LINE#         65
  C:06CDH         LINE#         66
  C:06CDH         LINE#         67
  C:06D0H         LINE#         68
  C:06D0H         LINE#         70
  C:06D0H         LINE#         72
  C:06D0H         LINE#         73
  C:06D0H         LINE#         74
  C:06D0H         LINE#         75
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 16


  C:06D0H         LINE#         78
  C:06D5H         LINE#         79
  C:06D5H         LINE#         80
  C:06D5H         LINE#         81
  C:06D8H         LINE#         82
  C:06D8H         LINE#         83
  C:06D8H         LINE#         84
  C:06D8H         LINE#         85
  C:06D8H         LINE#         87
  C:06E0H         LINE#         88
  C:06E0H         LINE#         89
  C:06E1H         LINE#         91
  C:06E9H         LINE#         92
  C:06E9H         LINE#         93
  C:06EAH         LINE#         94
  C:06F2H         LINE#         95
  C:06F2H         LINE#         96
  C:06F3H         LINE#         98
  C:0703H         LINE#         99
  C:0703H         LINE#         100
  C:0706H         LINE#         101
  C:0708H         LINE#         102
  C:0713H         LINE#         103
  C:0713H         LINE#         104
  C:0718H         LINE#         105
  C:0718H         LINE#         107
  C:0718H         LINE#         109
  C:0718H         LINE#         110
  C:0718H         LINE#         111
  C:0718H         LINE#         112
  C:0718H         LINE#         115
  C:071DH         LINE#         116
  C:071DH         LINE#         117
  C:071DH         LINE#         118
  C:0720H         LINE#         119
  C:0720H         LINE#         120
  C:0720H         LINE#         121
  C:0720H         LINE#         122
  C:0720H         LINE#         123
  -------         ENDPROC       KEYPROC
  -------         ENDMOD        KEYAPP

  -------         MODULE        COMMUNICATION
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:0000H         PUBLIC        Uart
  C:09A0H         PUBLIC        _CalcChecksum
  D:0099H         PUBLIC        SBUF
  C:049BH         PUBLIC        _TxdDataProc
  C:0A38H         PUBLIC        CommFunProc
  C:07A3H         PUBLIC        CommDealResponse
  B:0098H.4       PUBLIC        REN
  C:0AB9H         SYMBOL        Com003C
  C:0AB9H         SYMBOL        L?0061
  C:0ABCH         SYMBOL        L?0062
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 17


  -------         PROC          COM003C
  -------         ENDPROC       COM003C
  -------         PROC          _CALCCHECKSUM
  X:005AH         SYMBOL        CS_Buffer
  D:0005H         SYMBOL        Len
  -------         DO            
  D:0007H         SYMBOL        CS
  D:0006H         SYMBOL        j
  -------         ENDDO         
  C:09A0H         LINE#         104
  C:09ABH         LINE#         105
  C:09ABH         LINE#         106
  C:09ADH         LINE#         107
  C:09AEH         LINE#         109
  C:09B3H         LINE#         110
  C:09B3H         LINE#         111
  C:09C8H         LINE#         112
  C:09CBH         LINE#         113
  C:09CBH         LINE#         114
  -------         ENDPROC       _CALCCHECKSUM
  -------         PROC          COMMFUNPROC
  C:0A38H         LINE#         121
  C:0A38H         LINE#         122
  C:0A38H         LINE#         123
  C:0A40H         LINE#         124
  C:0A40H         LINE#         125
  C:0A41H         LINE#         153
  C:0A46H         LINE#         156
  C:0A4EH         LINE#         157
  C:0A4EH         LINE#         158
  C:0A51H         LINE#         159
  C:0A51H         LINE#         161
  C:0A51H         LINE#         162
  -------         ENDPROC       COMMFUNPROC
  C:054EH         SYMBOL        L?0063
  -------         PROC          _TXDDATAPROC
  X:0059H         SYMBOL        Ack
  -------         DO            
  D:0001H         SYMBOL        checksum
  -------         ENDDO         
  C:049BH         LINE#         169
  C:04A0H         LINE#         170
  C:04A0H         LINE#         172
  C:04A0H         LINE#         174
  C:04A5H         LINE#         175
  C:04A5H         LINE#         176
  C:04A7H         LINE#         177
  C:04A7H         LINE#         179
  C:04BCH         LINE#         180
  C:04BCH         LINE#         181
  C:04BCH         LINE#         182
  C:04BCH         LINE#         183
  C:04BCH         LINE#         184
  C:04BCH         LINE#         185
  C:04BCH         LINE#         186
  C:04BEH         LINE#         188
  C:04BEH         LINE#         189
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 18


  C:04C2H         LINE#         190
  C:04C4H         LINE#         192
  C:04C4H         LINE#         193
  C:04C9H         LINE#         194
  C:04C9H         LINE#         195
  C:04C9H         LINE#         198
  C:04C9H         LINE#         199
  C:04C9H         LINE#         207
  C:04CBH         LINE#         208
  C:04D3H         LINE#         209
  C:04D3H         LINE#         210
  C:04D5H         LINE#         212
  C:04D5H         LINE#         214
  C:04EAH         LINE#         215
  C:04EAH         LINE#         216
  C:04EAH         LINE#         217
  C:04EAH         LINE#         218
  C:04EAH         LINE#         219
  C:04EAH         LINE#         220
  C:04EAH         LINE#         221
  C:04ECH         LINE#         223
  C:04ECH         LINE#         224
  C:04F0H         LINE#         225
  C:04F2H         LINE#         227
  C:04F2H         LINE#         228
  C:04F7H         LINE#         229
  C:04F7H         LINE#         230
  C:04F7H         LINE#         233
  C:04FCH         LINE#         234
  C:0501H         LINE#         242
  C:0503H         LINE#         243
  C:050BH         LINE#         244
  C:050BH         LINE#         245
  C:0510H         LINE#         247
  C:0510H         LINE#         249
  C:0525H         LINE#         250
  C:0525H         LINE#         251
  C:0525H         LINE#         252
  C:0525H         LINE#         253
  C:0525H         LINE#         254
  C:0525H         LINE#         255
  C:0525H         LINE#         256
  C:0527H         LINE#         258
  C:0527H         LINE#         259
  C:052BH         LINE#         260
  C:052DH         LINE#         262
  C:052DH         LINE#         263
  C:0532H         LINE#         264
  C:0532H         LINE#         265
  C:0532H         LINE#         268
  C:0535H         LINE#         269
  C:053AH         LINE#         277
  C:053AH         LINE#         279
  C:053FH         LINE#         280
  C:0545H         LINE#         281
  C:0547H         LINE#         282
  C:054DH         LINE#         283
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 19


  -------         ENDPROC       _TXDDATAPROC
  -------         PROC          COMMDEALRESPONSE
  -------         DO            
  D:0007H         SYMBOL        rechecksum
  -------         ENDDO         
  C:07A3H         LINE#         290
  C:07A3H         LINE#         291
  C:07A3H         LINE#         294
  C:07ABH         LINE#         295
  C:07ABH         LINE#         296
  C:07B6H         LINE#         298
  C:07BDH         LINE#         299
  C:07BDH         LINE#         301
  C:07D5H         LINE#         302
  C:07D5H         LINE#         303
  C:07D5H         LINE#         304
  C:07DAH         LINE#         305
  C:07DAH         LINE#         306
  C:07DCH         LINE#         307
  C:07DCH         LINE#         308
  C:07E1H         LINE#         309
  C:07E1H         LINE#         310
  C:07E3H         LINE#         311
  C:07E3H         LINE#         312
  C:07E8H         LINE#         313
  C:07E8H         LINE#         314
  C:07EAH         LINE#         315
  C:07EAH         LINE#         316
  C:07EFH         LINE#         317
  C:07EFH         LINE#         318
  C:07F1H         LINE#         319
  C:07F1H         LINE#         320
  C:07F6H         LINE#         321
  C:07F6H         LINE#         322
  C:07F8H         LINE#         323
  C:07F8H         LINE#         324
  C:07FEH         LINE#         325
  C:0802H         LINE#         326
  C:0802H         LINE#         327
  C:0802H         LINE#         328
  C:0802H         LINE#         329
  C:0802H         LINE#         332
  C:0804H         LINE#         333
  C:0806H         LINE#         335
  C:0806H         LINE#         337
  C:080BH         LINE#         338
  C:080BH         LINE#         341
  C:0810H         LINE#         342
  C:0814H         LINE#         343
  C:0818H         LINE#         344
  C:0818H         LINE#         345
  -------         ENDPROC       COMMDEALRESPONSE
  -------         ENDMOD        COMMUNICATION

  -------         MODULE        LEDDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00EAH         PUBLIC        P1M0
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 20


  D:00E2H         PUBLIC        P1M1
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:0090H.2       PUBLIC        P1_2
  B:0090H.3       PUBLIC        P1_3
  B:0090H.4       PUBLIC        P1_4
  B:0090H.5       PUBLIC        P1_5
  B:0090H.6       PUBLIC        P1_6
  B:0090H.7       PUBLIC        P1_7
  C:0AF5H         PUBLIC        LED1_WorkOff
  C:0B07H         PUBLIC        LED2_WorkOff
  C:0B19H         PUBLIC        LED3_WorkOff
  C:0B2BH         PUBLIC        LED4_WorkOff
  C:0B3DH         PUBLIC        LED5_WorkOff
  C:0B4FH         PUBLIC        LED6_WorkOff
  C:0AECH         PUBLIC        LED1_WorkOn
  C:0AFEH         PUBLIC        LED2_WorkOn
  C:0B10H         PUBLIC        LED3_WorkOn
  C:0B22H         PUBLIC        LED4_WorkOn
  C:0B34H         PUBLIC        LED5_WorkOn
  C:0B46H         PUBLIC        LED6_WorkOn
  C:0B7BH         PUBLIC        InitAllLedDriverRam
  C:0AA7H         PUBLIC        InitAllLedOff
  -------         PROC          INITALLLEDDRIVERRAM
  C:0B7BH         LINE#         49
  C:0B7BH         LINE#         50
  C:0B7BH         LINE#         51
  C:0B80H         LINE#         65
  -------         ENDPROC       INITALLLEDDRIVERRAM
  -------         PROC          INITALLLEDOFF
  C:0AA7H         LINE#         72
  C:0AA7H         LINE#         73
  C:0AA7H         LINE#         74
  C:0AAAH         LINE#         75
  C:0AADH         LINE#         76
  C:0AB0H         LINE#         77
  C:0AB3H         LINE#         78
  C:0AB6H         LINE#         79
  -------         ENDPROC       INITALLLEDOFF
  -------         PROC          LED1_WORKON
  C:0AECH         LINE#         88
  C:0AECH         LINE#         89
  C:0AECH         LINE#         90
  C:0AF2H         LINE#         91
  C:0AF4H         LINE#         92
  -------         ENDPROC       LED1_WORKON
  -------         PROC          LED1_WORKOFF
  C:0AF5H         LINE#         99
  C:0AF5H         LINE#         100
  C:0AF5H         LINE#         101
  C:0AFBH         LINE#         102
  C:0AFDH         LINE#         103
  -------         ENDPROC       LED1_WORKOFF
  -------         PROC          LED2_WORKON
  C:0AFEH         LINE#         131
  C:0AFEH         LINE#         132
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 21


  C:0AFEH         LINE#         133
  C:0B04H         LINE#         134
  C:0B06H         LINE#         135
  -------         ENDPROC       LED2_WORKON
  -------         PROC          LED2_WORKOFF
  C:0B07H         LINE#         142
  C:0B07H         LINE#         143
  C:0B07H         LINE#         144
  C:0B0DH         LINE#         145
  C:0B0FH         LINE#         146
  -------         ENDPROC       LED2_WORKOFF
  -------         PROC          LED3_WORKON
  C:0B10H         LINE#         175
  C:0B10H         LINE#         176
  C:0B10H         LINE#         177
  C:0B16H         LINE#         178
  C:0B18H         LINE#         179
  -------         ENDPROC       LED3_WORKON
  -------         PROC          LED3_WORKOFF
  C:0B19H         LINE#         186
  C:0B19H         LINE#         187
  C:0B19H         LINE#         188
  C:0B1FH         LINE#         189
  C:0B21H         LINE#         190
  -------         ENDPROC       LED3_WORKOFF
  -------         PROC          LED4_WORKON
  C:0B22H         LINE#         218
  C:0B22H         LINE#         219
  C:0B22H         LINE#         220
  C:0B28H         LINE#         221
  C:0B2AH         LINE#         222
  -------         ENDPROC       LED4_WORKON
  -------         PROC          LED4_WORKOFF
  C:0B2BH         LINE#         229
  C:0B2BH         LINE#         230
  C:0B2BH         LINE#         231
  C:0B31H         LINE#         232
  C:0B33H         LINE#         233
  -------         ENDPROC       LED4_WORKOFF
  -------         PROC          LED5_WORKON
  C:0B34H         LINE#         262
  C:0B34H         LINE#         263
  C:0B34H         LINE#         264
  C:0B3AH         LINE#         265
  C:0B3CH         LINE#         266
  -------         ENDPROC       LED5_WORKON
  -------         PROC          LED5_WORKOFF
  C:0B3DH         LINE#         273
  C:0B3DH         LINE#         274
  C:0B3DH         LINE#         275
  C:0B43H         LINE#         276
  C:0B45H         LINE#         277
  -------         ENDPROC       LED5_WORKOFF
  -------         PROC          LED6_WORKON
  C:0B46H         LINE#         305
  C:0B46H         LINE#         306
  C:0B46H         LINE#         307
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 22


  C:0B4CH         LINE#         308
  C:0B4EH         LINE#         309
  -------         ENDPROC       LED6_WORKON
  -------         PROC          LED6_WORKOFF
  C:0B4FH         LINE#         316
  C:0B4FH         LINE#         317
  C:0B4FH         LINE#         318
  C:0B55H         LINE#         319
  C:0B57H         LINE#         320
  -------         ENDPROC       LED6_WORKOFF
  -------         ENDMOD        LEDDRIVER

  -------         MODULE        KEYDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00E4H         PUBLIC        P3M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:004CH         PUBLIC        KeyDrvStr
  B:00B0H.3       PUBLIC        P3_3
  B:00B0H.7       PUBLIC        P3_7
  C:01C6H         PUBLIC        KeyScan
  C:0886H         PUBLIC        InitKeyScanRam
  C:0AD6H         SYMBOL        Com002D
  C:0AD6H         SYMBOL        L?0046
  -------         PROC          COM002D
  -------         ENDPROC       COM002D
  -------         PROC          INITKEYSCANRAM
  -------         DO            
  D:0007H         SYMBOL        i
  -------         ENDDO         
  C:0886H         LINE#         48
  C:0886H         LINE#         49
  C:0886H         LINE#         52
  C:088BH         LINE#         53
  C:0896H         LINE#         54
  C:0896H         LINE#         55
  C:08A2H         LINE#         56
  C:08AEH         LINE#         57
  C:08BAH         LINE#         58
  C:08C6H         LINE#         59
  C:08D2H         LINE#         60
  C:08DEH         LINE#         61
  C:08E1H         LINE#         62
  -------         ENDPROC       INITKEYSCANRAM
  -------         PROC          KEYSCAN
  -------         DO            
  X:0059H         SYMBOL        tempbuff
  -------         ENDDO         
  C:01C6H         LINE#         69
  C:01C6H         LINE#         70
  C:01C6H         LINE#         73
  C:01D1H         LINE#         74
  C:01D1H         LINE#         75
  C:01D2H         LINE#         77
  C:01D8H         LINE#         78
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 23


  C:01DEH         LINE#         79
  C:01E0H         LINE#         80
  C:01E2H         LINE#         81
  C:01E3H         LINE#         82
  C:01E4H         LINE#         83
  C:01E5H         LINE#         84
  C:01E6H         LINE#         85
  C:01E7H         LINE#         86
  C:01E8H         LINE#         87
  C:01E9H         LINE#         88
  C:01EAH         LINE#         89
  C:01EBH         LINE#         90
  C:01ECH         LINE#         91
  C:01EFH         LINE#         92
  C:01EFH         LINE#         93
  C:01F4H         LINE#         94
  C:01F6H         LINE#         96
  C:01F6H         LINE#         97
  C:01FBH         LINE#         98
  C:01FBH         LINE#         99
  C:01FEH         LINE#         100
  C:01FEH         LINE#         101
  C:0204H         LINE#         102
  C:0206H         LINE#         104
  C:0206H         LINE#         105
  C:020BH         LINE#         106
  C:020BH         LINE#         110
  C:0217H         LINE#         111
  C:0217H         LINE#         112
  C:0219H         LINE#         113
  C:021EH         LINE#         114
  C:0220H         LINE#         116
  C:0220H         LINE#         117
  C:0223H         LINE#         118
  C:0228H         LINE#         119
  C:0228H         LINE#         120
  C:022AH         LINE#         122
  C:0237H         LINE#         123
  C:0237H         LINE#         124
  C:023CH         LINE#         126
  C:023FH         LINE#         127
  C:023FH         LINE#         128
  C:0245H         LINE#         129
  C:0247H         LINE#         130
  C:024EH         LINE#         131
  C:024EH         LINE#         132
  C:0251H         LINE#         133
  C:0257H         LINE#         134
  C:025AH         LINE#         135
  C:025CH         LINE#         137
  C:025CH         LINE#         138
  C:025FH         LINE#         139
  C:025FH         LINE#         140
  C:0261H         LINE#         142
  C:0261H         LINE#         143
  C:0265H         LINE#         144
  C:0265H         LINE#         145
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 24


  C:026BH         LINE#         146
  C:026DH         LINE#         147
  C:0270H         LINE#         148
  C:0270H         LINE#         149
  C:0276H         LINE#         150
  C:0278H         LINE#         152
  C:0278H         LINE#         153
  C:027DH         LINE#         154
  C:0281H         LINE#         155
  C:0285H         LINE#         156
  C:0289H         LINE#         157
  C:0289H         LINE#         158
  C:0289H         LINE#         159
  C:0289H         LINE#         160
  C:0289H         LINE#         163
  C:0295H         LINE#         164
  C:0295H         LINE#         165
  C:0297H         LINE#         166
  C:029CH         LINE#         167
  C:029DH         LINE#         169
  C:029DH         LINE#         170
  C:02A0H         LINE#         171
  C:02A5H         LINE#         172
  C:02A5H         LINE#         173
  C:02A7H         LINE#         175
  C:02B4H         LINE#         176
  C:02B4H         LINE#         177
  C:02B9H         LINE#         179
  C:02BCH         LINE#         180
  C:02BCH         LINE#         181
  C:02C2H         LINE#         182
  C:02C3H         LINE#         183
  C:02CAH         LINE#         184
  C:02CAH         LINE#         185
  C:02CDH         LINE#         186
  C:02D3H         LINE#         187
  C:02D6H         LINE#         188
  C:02D8H         LINE#         190
  C:02D8H         LINE#         191
  C:02DBH         LINE#         192
  C:02DBH         LINE#         193
  C:02DCH         LINE#         195
  C:02DCH         LINE#         196
  C:02E0H         LINE#         197
  C:02E0H         LINE#         198
  C:02E6H         LINE#         199
  C:02E7H         LINE#         200
  C:02EAH         LINE#         201
  C:02EAH         LINE#         202
  C:02F0H         LINE#         203
  C:02F1H         LINE#         205
  C:02F1H         LINE#         206
  C:02F6H         LINE#         207
  C:02FAH         LINE#         208
  C:02FEH         LINE#         209
  C:0302H         LINE#         210
  C:0302H         LINE#         211
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 25


  C:0302H         LINE#         212
  C:0302H         LINE#         213
  C:0302H         LINE#         214
  C:0302H         LINE#         215
  -------         ENDPROC       KEYSCAN
  -------         ENDMOD        KEYDRIVER

  -------         MODULE        UARTDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00E4H         PUBLIC        P3M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00A8H         PUBLIC        IEN0
  C:0926H         PUBLIC        _Uart0CalBaudrate
  B:0098H.0       PUBLIC        RI
  D:00CBH         PUBLIC        RCAP2H
  D:00CAH         PUBLIC        RCAP2L
  D:0087H         PUBLIC        PCON
  D:0098H         PUBLIC        SCON
  D:00CDH         PUBLIC        TH2
  D:00CCH         PUBLIC        TL2
  B:00C8H.2       PUBLIC        TR2
  C:09CCH         PUBLIC        Uart0Init
  B:0098H.4       PUBLIC        REN
  D:00C9H         PUBLIC        T2MOD
  D:00C8H         PUBLIC        T2CON
  D:009BH         PUBLIC        SADEN
  D:009AH         PUBLIC        SADDR
  -------         PROC          UART0INIT
  C:09CCH         LINE#         49
  C:09CCH         LINE#         50
  C:09CCH         LINE#         51
  C:09CFH         LINE#         52
  C:09D2H         LINE#         53
  C:09D5H         LINE#         59
  C:09D8H         LINE#         60
  C:09DBH         LINE#         61
  C:09DEH         LINE#         63
  C:09E1H         LINE#         64
  C:09E3H         LINE#         66
  C:09EAH         LINE#         67
  C:09EDH         LINE#         68
  C:09EFH         LINE#         69
  C:09F1H         LINE#         70
  C:09F4H         LINE#         71
  -------         ENDPROC       UART0INIT
  -------         PROC          _UART0CALBAUDRATE
  D:0006H         SYMBOL        baudratepar
  -------         DO            
  X:0059H         SYMBOL        CalBaudRateTemp
  -------         ENDDO         
  C:0926H         LINE#         80
  C:0926H         LINE#         81
  C:0926H         LINE#         84
  C:0944H         LINE#         89
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 26


  C:0947H         LINE#         90
  C:094AH         LINE#         91
  C:094DH         LINE#         92
  C:0950H         LINE#         93
  C:0953H         LINE#         94
  C:0956H         LINE#         95
  C:0959H         LINE#         96
  C:095BH         LINE#         97
  C:095DH         LINE#         99
  C:0960H         LINE#         100
  C:0963H         LINE#         101
  C:0965H         LINE#         102
  -------         ENDPROC       _UART0CALBAUDRATE
  -------         ENDMOD        UARTDRIVER

  -------         MODULE        PWMDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  C:0A94H         PUBLIC        Timer1_Init
  D:0089H         PUBLIC        TMOD
  B:00A8H.3       PUBLIC        ET1
  D:008DH         PUBLIC        TH1
  D:008BH         PUBLIC        TL1
  B:0088H.6       PUBLIC        TR1
  -------         PROC          TIMER1_INIT
  C:0A94H         LINE#         9
  C:0A94H         LINE#         10
  C:0A97H         LINE#         11
  C:0A9AH         LINE#         12
  C:0A9DH         LINE#         13
  C:0AA0H         LINE#         14
  C:0AA2H         LINE#         15
  C:0AA4H         LINE#         16
  C:0AA6H         LINE#         17
  -------         ENDPROC       TIMER1_INIT
  -------         ENDMOD        PWMDRIVER

  -------         MODULE        ?C?CLDOPTR
  C:0076H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?UIDIV
  C:00A3H         PUBLIC        ?C?UIDIV
  -------         ENDMOD        ?C?UIDIV

  -------         MODULE        ?C?SLDIV
  C:08E2H         PUBLIC        ?C?SLDIV
  -------         ENDMOD        ?C?SLDIV

  -------         MODULE        ?C?ULDIV
  C:0134H         PUBLIC        ?C?ULDIV
  -------         ENDMOD        ?C?ULDIV

Program Size: data=9.0 xdata=99 code=2944
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  18:11:12  PAGE 27


LINK/LOCATE RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
