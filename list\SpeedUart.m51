BL51 BANKED LINKER/LOCATER V6.22.2.0                                                    06/24/2025  17:50:43  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22.2.0, INVOKED BY:
D:\KEIL_V5\C51\BIN\BL51.EXE .\output\STARTUP.obj, .\output\Main.obj, .\output\Mcu.obj, .\output\Interrupt.obj, .\output\
>> LedApp.obj, .\output\KeyApp.obj, .\output\Communication.obj, .\output\LedDriver.obj, .\output\KeyDriver.obj, .\output
>> \UartDriver.obj, .\output\PwmDriver.obj TO .\output\SpeedUart PRINT (.\list\SpeedUart.m51) RAMSIZE (256)


MEMORY MODEL: LARGE


INPUT MODULES INCLUDED:
  .\output\STARTUP.obj (?C_STARTUP)
  .\output\Main.obj (MAIN)
  .\output\Mcu.obj (MCU)
  .\output\Interrupt.obj (INTERRUPT)
  .\output\LedApp.obj (LEDAPP)
  .\output\KeyApp.obj (KEYAPP)
  .\output\Communication.obj (COMMUNICATION)
  .\output\LedDriver.obj (LEDDRIVER)
  .\output\KeyDriver.obj (KEYDRIVER)
  .\output\UartDriver.obj (UARTDRIVER)
  .\output\PwmDriver.obj (PWMDRIVER)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C_INIT)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?CLDOPTR)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?UIDIV)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?SLDIV)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?ULDIV)


LINK MAP OF MODULE:  .\output\SpeedUart (?C_STARTUP)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            IDATA   0008H     0001H     UNIT         ?STACK

            * * * * * * *  X D A T A   M E M O R Y  * * * * * * *
            XDATA   0000H     003FH     UNIT         ?XD?COMMUNICATION
            XDATA   003FH     0011H     UNIT         ?XD?INTERRUPT
            XDATA   0050H     0011H     UNIT         ?XD?KEYDRIVER
            XDATA   0061H     0007H     UNIT         ?XD?LEDAPP
            XDATA   0068H     0006H     UNIT         ?XD?KEYAPP
            XDATA   006EH     0005H     UNIT         _XDATA_GROUP_
            XDATA   0073H     0004H     UNIT         ?XD?MAIN
            XDATA   0077H     0004H     UNIT         ?XD?MCU
            XDATA   007BH     0004H     UNIT         ?XD?LEDDRIVER
            XDATA   007FH     0004H     UNIT         ?XD?UARTDRIVER
            XDATA   0083H     0004H     UNIT         ?XD?PWMDRIVER

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0003H     ABSOLUTE     
            CODE    0006H     0005H     UNIT         ?PR?EX0_INT?INTERRUPT
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0005H     UNIT         ?PR?EX1_INT?INTERRUPT
            CODE    0013H     0003H     ABSOLUTE     
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 2


            CODE    0016H     0005H     UNIT         ?PR?TIMER2_INT?INTERRUPT
            CODE    001BH     0003H     ABSOLUTE     
            CODE    001EH     0004H     UNIT         ?PR?SCM_INT?INTERRUPT
                    0022H     0001H                  *** GAP ***
            CODE    0023H     0003H     ABSOLUTE     
            CODE    0026H     0004H     UNIT         ?PR?PWM_INT?INTERRUPT
                    002AH     0001H                  *** GAP ***
            CODE    002BH     0003H     ABSOLUTE     
            CODE    002EH     001AH     UNIT         ?PR?INITT0?MCU
            CODE    0048H     0003H     UNIT         ?PR?INITCPUACSET?MCU
            CODE    004BH     0003H     ABSOLUTE     
            CODE    004EH     000DH     UNIT         ?PR?MONITORCPUTIMER?MCU
            CODE    005BH     0003H     ABSOLUTE     
            CODE    005EH     0004H     UNIT         ?PR?ELPD_INT?INTERRUPT
                    0062H     0001H                  *** GAP ***
            CODE    0063H     0003H     ABSOLUTE     
            CODE    0066H     000CH     UNIT         ?PR?INITINTERRUPTRAM?INTERRUPT
                    0072H     0001H                  *** GAP ***
            CODE    0073H     0003H     ABSOLUTE     
            CODE    0076H     0150H     UNIT         ?C?LIB_CODE
            CODE    01C6H     013DH     UNIT         ?PR?KEYSCAN?KEYDRIVER
            CODE    0303H     00D0H     UNIT         ?PR?EUART0_INT?INTERRUPT
            CODE    03D3H     00BFH     UNIT         ?PR?_TXDDATAPROC?COMMUNICATION
            CODE    0492H     00BCH     UNIT         ?PR?TIMER0_INT?INTERRUPT
            CODE    054EH     00A5H     UNIT         ?C_INITSEG
            CODE    05F3H     008CH     UNIT         ?C_C51STARTUP
            CODE    067FH     008BH     UNIT         ?PR?KEYPROC?KEYAPP
            CODE    070AH     0076H     UNIT         ?PR?LEDAPP?LEDAPP
            CODE    0780H     0076H     UNIT         ?PR?COMMDEALRESPONSE?COMMUNICATION
            CODE    07F6H     006DH     UNIT         ?PR?TIMER1_INT?INTERRUPT
            CODE    0863H     005CH     UNIT         ?PR?INITKEYSCANRAM?KEYDRIVER
            CODE    08BFH     004FH     UNIT         ?PR?MAIN?MAIN
            CODE    090EH     0044H     UNIT         ?C?LDIV
            CODE    0952H     0040H     UNIT         ?PR?_UART0CALBAUDRATE?UARTDRIVER
            CODE    0992H     003AH     UNIT         ?PR?INTERRUPT
            CODE    09CCH     002CH     UNIT         ?PR?_CALCCHECKSUM?COMMUNICATION
            CODE    09F8H     0029H     UNIT         ?PR?UART0INIT?UARTDRIVER
            CODE    0A21H     0022H     UNIT         ?PR?KEYAPP
            CODE    0A43H     0021H     UNIT         ?PR?_DELAYNMS?MCU
            CODE    0A64H     001AH     UNIT         ?PR?COMMFUNPROC?COMMUNICATION
            CODE    0A7EH     0019H     UNIT         ?PR?INITPORT?MCU
            CODE    0A97H     0014H     UNIT         ?PR?INITT1?MCU
            CODE    0AABH     0013H     UNIT         ?PR?TIMER1_INIT?PWMDRIVER
            CODE    0ABEH     0012H     UNIT         ?PR?INITALLLEDOFF?LEDDRIVER
            CODE    0AD0H     000FH     UNIT         ?PR?COMMUNICATION
            CODE    0ADFH     000EH     UNIT         ?PR?INITSYS?MCU
            CODE    0AEDH     000CH     UNIT         ?PR?KEYDRIVER
            CODE    0AF9H     000AH     UNIT         ?PR?INITINTERRUPTPRIORITYLEVEL?MCU
            CODE    0B03H     0009H     UNIT         ?PR?LED1_WORKON?LEDDRIVER
            CODE    0B0CH     0009H     UNIT         ?PR?LED1_WORKOFF?LEDDRIVER
            CODE    0B15H     0009H     UNIT         ?PR?LED2_WORKON?LEDDRIVER
            CODE    0B1EH     0009H     UNIT         ?PR?LED2_WORKOFF?LEDDRIVER
            CODE    0B27H     0009H     UNIT         ?PR?LED3_WORKON?LEDDRIVER
            CODE    0B30H     0009H     UNIT         ?PR?LED3_WORKOFF?LEDDRIVER
            CODE    0B39H     0009H     UNIT         ?PR?LED4_WORKON?LEDDRIVER
            CODE    0B42H     0009H     UNIT         ?PR?LED4_WORKOFF?LEDDRIVER
            CODE    0B4BH     0009H     UNIT         ?PR?LED5_WORKON?LEDDRIVER
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 3


            CODE    0B54H     0009H     UNIT         ?PR?LED5_WORKOFF?LEDDRIVER
            CODE    0B5DH     0009H     UNIT         ?PR?LED6_WORKON?LEDDRIVER
            CODE    0B66H     0009H     UNIT         ?PR?LED6_WORKOFF?LEDDRIVER
            CODE    0B6FH     0008H     UNIT         ?PR?INITPWM?MCU
            CODE    0B77H     0007H     UNIT         ?PR?INITINT1?MCU
            CODE    0B7EH     0007H     UNIT         ?PR?INITINT2?MCU
            CODE    0B85H     0007H     UNIT         ?PR?EX2_INT?INTERRUPT
            CODE    0B8CH     0006H     UNIT         ?PR?INITKEYPROCRAM?KEYAPP
            CODE    0B92H     0006H     UNIT         ?PR?INITALLLEDDRIVERRAM?LEDDRIVER
            CODE    0B98H     0003H     UNIT         ?PR?INITALLLEDRAMAPP?LEDAPP



OVERLAY MAP OF MODULE:   .\output\SpeedUart (?C_STARTUP)


SEGMENT                                      XDATA_GROUP
  +--> CALLED SEGMENT                      START    LENGTH
----------------------------------------------------------
?C_C51STARTUP                              -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                              -----    -----
  +--> ?PR?INITSYS?MCU
  +--> ?PR?_DELAYNMS?MCU
  +--> ?PR?INITPORT?MCU
  +--> ?PR?INITKEYSCANRAM?KEYDRIVER
  +--> ?PR?INITKEYPROCRAM?KEYAPP
  +--> ?PR?INITINTERRUPTRAM?INTERRUPT
  +--> ?PR?INITALLLEDOFF?LEDDRIVER
  +--> ?PR?INITALLLEDRAMAPP?LEDAPP
  +--> ?PR?INITT0?MCU
  +--> ?PR?INITT1?MCU
  +--> ?PR?INITINT1?MCU
  +--> ?PR?INITINT2?MCU
  +--> ?PR?INITPWM?MCU
  +--> ?PR?UART0INIT?UARTDRIVER
  +--> ?PR?MONITORCPUTIMER?MCU
  +--> ?PR?INITCPUACSET?MCU
  +--> ?PR?KEYSCAN?KEYDRIVER
  +--> ?PR?KEYPROC?KEYAPP
  +--> ?PR?LEDAPP?LEDAPP
  +--> ?PR?COMMFUNPROC?COMMUNICATION

?PR?INITSYS?MCU                            -----    -----
  +--> ?PR?INITINTERRUPTPRIORITYLEVEL?MCU

?PR?INITINTERRUPTRAM?INTERRUPT             -----    -----
  +--> ?PR?INTERRUPT

?PR?INITALLLEDOFF?LEDDRIVER                -----    -----
  +--> ?PR?LED1_WORKOFF?LEDDRIVER
  +--> ?PR?LED2_WORKOFF?LEDDRIVER
  +--> ?PR?LED3_WORKOFF?LEDDRIVER
  +--> ?PR?LED4_WORKOFF?LEDDRIVER
  +--> ?PR?LED5_WORKOFF?LEDDRIVER
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 4


  +--> ?PR?LED6_WORKOFF?LEDDRIVER

?PR?INITALLLEDRAMAPP?LEDAPP                -----    -----
  +--> ?PR?INITALLLEDDRIVERRAM?LEDDRIVER

?PR?UART0INIT?UARTDRIVER                   -----    -----
  +--> ?PR?_UART0CALBAUDRATE?UARTDRIVER

?PR?_UART0CALBAUDRATE?UARTDRIVER           006EH    0002H

?PR?KEYSCAN?KEYDRIVER                      006EH    0002H
  +--> ?PR?KEYDRIVER

?PR?KEYPROC?KEYAPP                         -----    -----
  +--> ?PR?KEYAPP
  +--> ?PR?_TXDDATAPROC?COMMUNICATION

?PR?_TXDDATAPROC?COMMUNICATION             006EH    0001H
  +--> ?PR?COMMUNICATION
  +--> ?PR?_CALCCHECKSUM?COMMUNICATION

?PR?_CALCCHECKSUM?COMMUNICATION            006FH    0003H

?PR?LEDAPP?LEDAPP                          -----    -----
  +--> ?PR?INITALLLEDOFF?LEDDRIVER
  +--> ?PR?LED1_WORKON?LEDDRIVER
  +--> ?PR?LED2_WORKON?LEDDRIVER
  +--> ?PR?LED3_WORKON?LEDDRIVER
  +--> ?PR?LED4_WORKON?LEDDRIVER
  +--> ?PR?LED5_WORKON?LEDDRIVER
  +--> ?PR?LED6_WORKON?LEDDRIVER

?PR?COMMFUNPROC?COMMUNICATION              -----    -----
  +--> ?PR?_TXDDATAPROC?COMMUNICATION
  +--> ?PR?COMMDEALRESPONSE?COMMUNICATION

?PR?COMMDEALRESPONSE?COMMUNICATION         -----    -----
  +--> ?PR?_CALCCHECKSUM?COMMUNICATION
  +--> ?PR?_TXDDATAPROC?COMMUNICATION

*** NEW ROOT ***************************************************

?PR?TIMER0_INT?INTERRUPT                   -----    -----
  +--> ?PR?INTERRUPT

*** NEW ROOT ***************************************************

?PR?EUART0_INT?INTERRUPT                   0072H    0001H
  +--> ?PR?INTERRUPT



IGNORED SYMBOLS:
   COUNT
   DUTY
   DUTY_SHADOW
   HIGH_COUNT
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 5





SYMBOL TABLE OF MODULE:  .\output\SpeedUart (?C_STARTUP)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        ?C_STARTUP
  C:05F3H         SEGMENT       ?C_C51STARTUP
  I:0008H         SEGMENT       ?STACK
  C:0000H         PUBLIC        ?C_STARTUP
  D:00E0H         SYMBOL        ACC
  D:00F0H         SYMBOL        B
  N:3026H         SYMBOL        CODE_SIZE
  D:0083H         SYMBOL        DPH
  D:0082H         SYMBOL        DPL
  N:0F9AH         SYMBOL        FILLING_A5_NUM
  C:05FFH         SYMBOL        FILL_CODE
  N:0000H         SYMBOL        IBPSTACK
  N:0100H         SYMBOL        IBPSTACKTOP
  N:0080H         SYMBOL        IDATALEN
  C:05F6H         SYMBOL        IDATALOOP
  N:0000H         SYMBOL        PBPSTACK
  N:0000H         SYMBOL        PBPSTACKTOP
  N:0000H         SYMBOL        PDATALEN
  N:0000H         SYMBOL        PDATASTART
  N:0000H         SYMBOL        PPAGE
  N:0000H         SYMBOL        PPAGEENABLE
  D:00A0H         SYMBOL        PPAGE_SFR
  N:3FC0H         SYMBOL        ROM_SIZE
  D:0081H         SYMBOL        SP
  C:05F3H         SYMBOL        STARTUP1
  N:0000H         SYMBOL        XBPSTACK
  N:0000H         SYMBOL        XBPSTACKTOP
  N:0000H         SYMBOL        XDATALEN
  N:0000H         SYMBOL        XDATASTART
  C:0000H         LINE#         107
  C:05F3H         LINE#         114
  C:05F5H         LINE#         115
  C:05F6H         LINE#         116
  C:05F7H         LINE#         117
  C:05F9H         LINE#         166
  C:05FCH         LINE#         170
  -------         ENDMOD        ?C_STARTUP

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IEN0
  D:00A9H         PUBLIC        IEN1
  C:08BFH         PUBLIC        main
  X:0073H         PUBLIC        count
  X:0074H         PUBLIC        high_count
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 6


  X:0075H         PUBLIC        duty_shadow
  X:0076H         PUBLIC        duty
  -------         PROC          MAIN
  C:08BFH         LINE#         50
  C:08BFH         LINE#         51
  C:08BFH         LINE#         52
  C:08C1H         LINE#         53
  C:08C4H         LINE#         54
  C:08CBH         LINE#         55
  C:08CEH         LINE#         56
  C:08D1H         LINE#         57
  C:08D4H         LINE#         58
  C:08D7H         LINE#         59
  C:08DAH         LINE#         60
  C:08DDH         LINE#         61
  C:08E0H         LINE#         62
  C:08E3H         LINE#         63
  C:08E6H         LINE#         64
  C:08E9H         LINE#         65
  C:08ECH         LINE#         66
  C:08EFH         LINE#         67
  C:08F2H         LINE#         68
  C:08F5H         LINE#         69
  C:08F7H         LINE#         80
  C:08F7H         LINE#         81
  C:08F7H         LINE#         82
  C:08FAH         LINE#         83
  C:08FDH         LINE#         84
  C:0900H         LINE#         85
  C:0903H         LINE#         86
  C:0906H         LINE#         87
  C:0909H         LINE#         88
  C:090CH         LINE#         89
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        MCU
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00EAH         PUBLIC        P1M0
  D:00E2H         PUBLIC        P1M1
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00EDH         PUBLIC        P4M0
  D:00E4H         PUBLIC        P3M1
  D:00E5H         PUBLIC        P4M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  D:00A9H         PUBLIC        IEN1
  D:00B4H         PUBLIC        IPH0
  D:00B5H         PUBLIC        IPH1
  D:00E8H         PUBLIC        EXF0
  D:00B8H         PUBLIC        IPL0
  D:00B9H         PUBLIC        IPL1
  C:002EH         PUBLIC        InitT0
  C:004EH         PUBLIC        MonitorCpuTimer
  C:0A97H         PUBLIC        InitT1
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 7


  C:0A43H         PUBLIC        _DelayNms
  X:0077H         PUBLIC        count
  X:0078H         PUBLIC        high_count
  X:0079H         PUBLIC        duty_shadow
  C:0B77H         PUBLIC        InitINT1
  C:0B7EH         PUBLIC        InitINT2
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:00B1H         PUBLIC        RSTSTAT
  D:00D3H         PUBLIC        PWMD
  D:00B2H         PUBLIC        CLKCON
  C:0A7EH         PUBLIC        InitPort
  B:0088H.3       PUBLIC        IE1
  D:00B3H         PUBLIC        LPDCON
  D:00D2H         PUBLIC        PWMP
  X:007AH         PUBLIC        duty
  B:0088H.5       PUBLIC        TF0
  B:0088H.7       PUBLIC        TF1
  C:0B6FH         PUBLIC        InitPwm
  D:008CH         PUBLIC        TH0
  D:008DH         PUBLIC        TH1
  B:0088H.2       PUBLIC        IT1
  B:00A8H.2       PUBLIC        EX1
  C:0AF9H         PUBLIC        InitInterruptPriorityLevel
  D:008AH         PUBLIC        TL0
  D:008BH         PUBLIC        TL1
  B:0088H.4       PUBLIC        TR0
  D:00D1H         PUBLIC        PWMCON
  C:0ADFH         PUBLIC        InitSys
  B:0088H.6       PUBLIC        TR1
  D:00CEH         PUBLIC        TCON1
  C:0048H         PUBLIC        InitCpuACSet
  -------         PROC          INITSYS
  C:0ADFH         LINE#         50
  C:0ADFH         LINE#         51
  C:0ADFH         LINE#         52
  C:0AE1H         LINE#         53
  C:0AE4H         LINE#         54
  C:0AE6H         LINE#         55
  C:0AE9H         LINE#         56
  C:0AECH         LINE#         57
  -------         ENDPROC       INITSYS
  -------         PROC          INITCPUACSET
  C:0048H         LINE#         64
  C:0048H         LINE#         65
  C:0048H         LINE#         66
  C:004AH         LINE#         67
  -------         ENDPROC       INITCPUACSET
  -------         PROC          INITPORT
  C:0A7EH         LINE#         74
  C:0A7EH         LINE#         75
  C:0A7EH         LINE#         76
  C:0A81H         LINE#         77
  C:0A84H         LINE#         78
  C:0A86H         LINE#         80
  C:0A89H         LINE#         81
  C:0A8CH         LINE#         83
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 8


  C:0A8EH         LINE#         84
  C:0A91H         LINE#         86
  C:0A93H         LINE#         87
  C:0A96H         LINE#         88
  -------         ENDPROC       INITPORT
  -------         PROC          INITINTERRUPTPRIORITYLEVEL
  C:0AF9H         LINE#         96
  C:0AF9H         LINE#         97
  C:0AF9H         LINE#         98
  C:0AFCH         LINE#         99
  C:0AFEH         LINE#         100
  C:0B00H         LINE#         101
  C:0B02H         LINE#         102
  -------         ENDPROC       INITINTERRUPTPRIORITYLEVEL
  -------         PROC          INITT0
  C:002EH         LINE#         109
  C:002EH         LINE#         110
  C:002EH         LINE#         111
  C:0031H         LINE#         113
  C:0034H         LINE#         114
  C:0037H         LINE#         116
  C:003AH         LINE#         117
  C:003DH         LINE#         119
  C:0040H         LINE#         120
  C:0043H         LINE#         121
  C:0045H         LINE#         122
  C:0047H         LINE#         123
  -------         ENDPROC       INITT0
  -------         PROC          INITT1
  C:0A97H         LINE#         131
  C:0A97H         LINE#         132
  C:0A97H         LINE#         133
  C:0A9AH         LINE#         135
  C:0A9DH         LINE#         136
  C:0A9DH         LINE#         139
  C:0AA0H         LINE#         140
  C:0AA0H         LINE#         143
  C:0AA3H         LINE#         144
  C:0AA6H         LINE#         145
  C:0AA8H         LINE#         146
  C:0AAAH         LINE#         147
  -------         ENDPROC       INITT1
  -------         PROC          INITINT1
  C:0B77H         LINE#         155
  C:0B77H         LINE#         156
  C:0B77H         LINE#         157
  C:0B79H         LINE#         158
  C:0B7BH         LINE#         159
  C:0B7DH         LINE#         160
  -------         ENDPROC       INITINT1
  -------         PROC          INITINT2
  C:0B7EH         LINE#         167
  C:0B7EH         LINE#         168
  C:0B7EH         LINE#         169
  C:0B81H         LINE#         170
  C:0B84H         LINE#         171
  -------         ENDPROC       INITINT2
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 9


  -------         PROC          INITPWM
  C:0B6FH         LINE#         179
  C:0B6FH         LINE#         180
  C:0B6FH         LINE#         185
  C:0B72H         LINE#         186
  C:0B74H         LINE#         187
  C:0B76H         LINE#         188
  -------         ENDPROC       INITPWM
  -------         PROC          MONITORCPUTIMER
  C:004EH         LINE#         196
  C:004EH         LINE#         197
  C:004EH         LINE#         198
  C:0056H         LINE#         199
  C:0056H         LINE#         200
  C:0057H         LINE#         202
  C:005AH         LINE#         203
  C:005AH         LINE#         204
  -------         ENDPROC       MONITORCPUTIMER
  -------         PROC          _DELAYNMS
  D:0006H         SYMBOL        num
  -------         DO            
  D:0004H         SYMBOL        i
  D:0002H         SYMBOL        j
  -------         ENDDO         
  C:0A43H         LINE#         212
  C:0A43H         LINE#         213
  C:0A43H         LINE#         216
  C:0A4DH         LINE#         217
  C:0A4DH         LINE#         218
  C:0A50H         LINE#         219
  C:0A50H         LINE#         220
  C:0A51H         LINE#         221
  C:0A5CH         LINE#         222
  C:0A63H         LINE#         223
  -------         ENDPROC       _DELAYNMS
  -------         ENDMOD        MCU

  -------         MODULE        INTERRUPT
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:0303H         PUBLIC        EUART0_INT
  C:0492H         PUBLIC        Timer0_INT
  D:0090H         PUBLIC        P1
  C:07F6H         PUBLIC        Timer1_INT
  C:0016H         PUBLIC        Timer2_INT
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:0066H         PUBLIC        InitInterruptRam
  D:00A9H         PUBLIC        IEN1
  D:00E8H         PUBLIC        EXF0
  B:00C8H.6       PUBLIC        EXF2
  C:0006H         PUBLIC        EX0_INT
  C:000EH         PUBLIC        EX1_INT
  B:0098H.0       PUBLIC        RI
  C:0B85H         PUBLIC        EX2_INT
  B:0098H.1       PUBLIC        TI
  X:003FH         PUBLIC        count
  X:0040H         PUBLIC        high_count
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 10


  C:001EH         PUBLIC        SCM_INT
  D:0099H         PUBLIC        SBUF
  X:0041H         PUBLIC        duty_shadow
  D:00B2H         PUBLIC        CLKCON
  B:0088H.1       PUBLIC        IE0
  B:0088H.3       PUBLIC        IE1
  C:0026H         PUBLIC        PWM_INT
  D:00B3H         PUBLIC        LPDCON
  X:0042H         PUBLIC        duty
  X:0043H         PUBLIC        TimeFlagStr
  B:0088H.5       PUBLIC        TF0
  B:0088H.7       PUBLIC        TF1
  B:00C8H.7       PUBLIC        TF2
  D:008CH         PUBLIC        TH0
  B:00A8H.0       PUBLIC        EX0
  D:008DH         PUBLIC        TH1
  B:00A8H.2       PUBLIC        EX1
  D:008AH         PUBLIC        TL0
  D:008BH         PUBLIC        TL1
  B:0088H.4       PUBLIC        TR0
  D:00D1H         PUBLIC        PWMCON
  B:0088H.6       PUBLIC        TR1
  B:0098H.4       PUBLIC        REN
  B:00B0H.0       PUBLIC        PWM_IN
  C:005EH         PUBLIC        ELPD_INT
  C:0992H         SYMBOL        Com0028
  C:0992H         SYMBOL        L?0041
  C:099EH         SYMBOL        L?0042
  C:09ACH         SYMBOL        L?0043
  C:09B4H         SYMBOL        L?0044
  C:09B7H         SYMBOL        L?0045
  C:09C1H         SYMBOL        L?0046
  -------         PROC          COM0028
  -------         ENDPROC       COM0028
  -------         PROC          INITINTERRUPTRAM
  C:0066H         LINE#         46
  C:0066H         LINE#         47
  C:0066H         LINE#         48
  C:006AH         LINE#         50
  C:006AH         LINE#         51
  C:006AH         LINE#         52
  C:006AH         LINE#         54
  C:006AH         LINE#         55
  C:006DH         LINE#         57
  C:006EH         LINE#         58
  C:006EH         LINE#         60
  C:006EH         LINE#         61
  C:0071H         LINE#         62
  -------         ENDPROC       INITINTERRUPTRAM
  -------         PROC          EX0_INT
  C:0006H         LINE#         69
  C:0006H         LINE#         71
  C:0008H         LINE#         72
  C:000AH         LINE#         73
  -------         ENDPROC       EX0_INT
  -------         PROC          TIMER0_INT
  C:0492H         LINE#         80
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 11


  C:049AH         LINE#         82
  C:049CH         LINE#         83
  C:049EH         LINE#         84
  C:04A1H         LINE#         85
  C:04A4H         LINE#         86
  C:04A6H         LINE#         92
  C:04ACH         LINE#         94
  C:04B2H         LINE#         95
  C:04BFH         LINE#         96
  C:04BFH         LINE#         97
  C:04C1H         LINE#         99
  C:04C2H         LINE#         100
  C:04C7H         LINE#         101
  C:04C7H         LINE#         102
  C:04C9H         LINE#         103
  C:04C9H         LINE#         105
  C:04CFH         LINE#         106
  C:04D9H         LINE#         107
  C:04D9H         LINE#         108
  C:04DBH         LINE#         109
  C:04DEH         LINE#         111
  C:04E2H         LINE#         113
  C:04E5H         LINE#         114
  C:04EAH         LINE#         115
  C:04EAH         LINE#         116
  C:04ECH         LINE#         117
  C:04EFH         LINE#         119
  C:04F5H         LINE#         120
  C:04F8H         LINE#         121
  C:04F8H         LINE#         122
  C:04FBH         LINE#         123
  C:04FDH         LINE#         124
  C:04FDH         LINE#         127
  C:050BH         LINE#         128
  C:051AH         LINE#         129
  C:051AH         LINE#         130
  C:051EH         LINE#         131
  C:0521H         LINE#         133
  C:052FH         LINE#         134
  C:053EH         LINE#         135
  C:053EH         LINE#         136
  C:0542H         LINE#         137
  C:0545H         LINE#         138
  C:0545H         LINE#         139
  C:0545H         LINE#         140
  C:0545H         LINE#         165
  C:0545H         LINE#         166
  -------         ENDPROC       TIMER0_INT
  -------         PROC          EX1_INT
  C:000EH         LINE#         173
  C:000EH         LINE#         175
  C:0010H         LINE#         176
  C:0012H         LINE#         177
  -------         ENDPROC       EX1_INT
  -------         PROC          TIMER1_INT
  C:07F6H         LINE#         184
  C:080DH         LINE#         186
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 12


  C:080FH         LINE#         187
  C:0811H         LINE#         188
  C:0814H         LINE#         189
  C:0817H         LINE#         190
  C:0819H         LINE#         192
  C:081FH         LINE#         193
  C:0826H         LINE#         195
  C:0834H         LINE#         196
  C:0847H         LINE#         199
  C:084CH         LINE#         200
  C:084EH         LINE#         201
  C:084EH         LINE#         202
  -------         ENDPROC       TIMER1_INT
  -------         PROC          EUART0_INT
  -------         DO            
  X:0072H         SYMBOL        TempSBUF
  -------         ENDDO         
  C:0303H         LINE#         298
  C:0312H         LINE#         302
  C:0318H         LINE#         303
  C:0318H         LINE#         304
  C:031AH         LINE#         306
  C:0320H         LINE#         307
  C:0326H         LINE#         309
  C:032DH         LINE#         310
  C:032DH         LINE#         311
  C:0336H         LINE#         312
  C:0336H         LINE#         313
  C:0336H         LINE#         314
  C:0344H         LINE#         315
  C:0347H         LINE#         316
  C:034DH         LINE#         318
  C:0350H         LINE#         320
  C:0350H         LINE#         325
  C:0350H         LINE#         326
  C:0350H         LINE#         327
  C:0355H         LINE#         328
  C:0355H         LINE#         329
  C:035EH         LINE#         330
  C:035EH         LINE#         331
  C:035EH         LINE#         332
  C:035EH         LINE#         333
  C:0361H         LINE#         334
  C:0367H         LINE#         336
  C:0369H         LINE#         338
  C:0369H         LINE#         339
  C:036EH         LINE#         340
  C:0372H         LINE#         341
  C:0376H         LINE#         342
  C:0378H         LINE#         343
  C:0378H         LINE#         344
  C:0378H         LINE#         345
  C:0380H         LINE#         346
  C:0380H         LINE#         347
  C:0385H         LINE#         348
  C:0388H         LINE#         350
  C:0393H         LINE#         351
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 13


  C:0393H         LINE#         352
  C:0396H         LINE#         353
  C:0396H         LINE#         354
  C:0399H         LINE#         356
  C:039BH         LINE#         357
  C:039BH         LINE#         358
  C:039DH         LINE#         360
  C:039DH         LINE#         361
  C:039DH         LINE#         362
  C:03A0H         LINE#         363
  C:03A2H         LINE#         364
  C:03A2H         LINE#         365
  C:03A2H         LINE#         367
  C:03A5H         LINE#         368
  C:03A5H         LINE#         369
  C:03A7H         LINE#         371
  C:03B2H         LINE#         372
  C:03B2H         LINE#         373
  C:03B5H         LINE#         374
  C:03C2H         LINE#         375
  C:03C4H         LINE#         377
  C:03C4H         LINE#         378
  C:03C6H         LINE#         379
  C:03C6H         LINE#         380
  C:03C6H         LINE#         381
  -------         ENDPROC       EUART0_INT
  -------         PROC          TIMER2_INT
  C:0016H         LINE#         486
  C:0016H         LINE#         488
  C:0018H         LINE#         489
  C:001AH         LINE#         490
  -------         ENDPROC       TIMER2_INT
  -------         PROC          EX2_INT
  C:0B85H         LINE#         497
  C:0B85H         LINE#         499
  C:0B88H         LINE#         500
  C:0B8BH         LINE#         501
  -------         ENDPROC       EX2_INT
  -------         PROC          SCM_INT
  C:001EH         LINE#         508
  C:001EH         LINE#         510
  C:0021H         LINE#         511
  -------         ENDPROC       SCM_INT
  -------         PROC          PWM_INT
  C:0026H         LINE#         518
  C:0026H         LINE#         520
  C:0029H         LINE#         521
  -------         ENDPROC       PWM_INT
  -------         PROC          ELPD_INT
  C:005EH         LINE#         528
  C:005EH         LINE#         530
  C:0061H         LINE#         531
  -------         ENDPROC       ELPD_INT
  -------         ENDMOD        INTERRUPT

  -------         MODULE        LEDAPP
  C:0000H         SYMBOL        _ICE_DUMMY_
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 14


  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:0061H         PUBLIC        LedWorkGroupStr
  C:0B98H         PUBLIC        InitAllLedRamApp
  X:0064H         PUBLIC        count
  X:0065H         PUBLIC        high_count
  X:0066H         PUBLIC        duty_shadow
  C:070AH         PUBLIC        LedApp
  X:0067H         PUBLIC        duty
  -------         PROC          INITALLLEDRAMAPP
  C:0B98H         LINE#         50
  C:0B98H         LINE#         51
  C:0B98H         LINE#         52
  -------         ENDPROC       INITALLLEDRAMAPP
  -------         PROC          LEDAPP
  C:070AH         LINE#         60
  C:070AH         LINE#         61
  C:070AH         LINE#         62
  C:0712H         LINE#         63
  C:0712H         LINE#         64
  C:0713H         LINE#         66
  C:072BH         LINE#         67
  C:072BH         LINE#         68
  C:072BH         LINE#         69
  C:072EH         LINE#         70
  C:0731H         LINE#         71
  C:0731H         LINE#         73
  C:0731H         LINE#         74
  C:0734H         LINE#         75
  C:0737H         LINE#         76
  C:073AH         LINE#         77
  C:073AH         LINE#         79
  C:073AH         LINE#         80
  C:073DH         LINE#         81
  C:0740H         LINE#         82
  C:0743H         LINE#         83
  C:0746H         LINE#         84
  C:0746H         LINE#         86
  C:0746H         LINE#         87
  C:0749H         LINE#         88
  C:074CH         LINE#         89
  C:074FH         LINE#         90
  C:0752H         LINE#         91
  C:0755H         LINE#         92
  C:0755H         LINE#         94
  C:0755H         LINE#         95
  C:0758H         LINE#         96
  C:075BH         LINE#         97
  C:075EH         LINE#         98
  C:0761H         LINE#         99
  C:0764H         LINE#         100
  C:0767H         LINE#         101
  C:0767H         LINE#         103
  C:0767H         LINE#         104
  C:076AH         LINE#         105
  C:076DH         LINE#         106
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 15


  C:0770H         LINE#         107
  C:0773H         LINE#         108
  C:0776H         LINE#         109
  C:0779H         LINE#         110
  C:077CH         LINE#         111
  C:077CH         LINE#         113
  C:077CH         LINE#         114
  C:077FH         LINE#         115
  C:077FH         LINE#         116
  C:077FH         LINE#         117
  C:077FH         LINE#         118
  -------         ENDPROC       LEDAPP
  -------         ENDMOD        LEDAPP

  -------         MODULE        KEYAPP
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:0068H         PUBLIC        count
  X:0069H         PUBLIC        high_count
  X:006AH         PUBLIC        duty_shadow
  C:067FH         PUBLIC        KeyProc
  X:006BH         PUBLIC        duty
  B:0098H.4       PUBLIC        REN
  X:006CH         PUBLIC        g_last_speed_level
  C:0B8CH         PUBLIC        InitKeyProcRam
  X:006DH         PUBLIC        KeyProcStr
  C:0A21H         SYMBOL        Com000F
  C:0A21H         SYMBOL        L?0016
  -------         PROC          COM000F
  -------         ENDPROC       COM000F
  -------         PROC          INITKEYPROCRAM
  C:0B8CH         LINE#         40
  C:0B8CH         LINE#         41
  C:0B8CH         LINE#         42
  C:0B91H         LINE#         43
  -------         ENDPROC       INITKEYPROCRAM
  C:06FEH         SYMBOL        L?0017
  -------         PROC          KEYPROC
  C:067FH         LINE#         50
  C:067FH         LINE#         51
  C:067FH         LINE#         52
  C:0687H         LINE#         53
  C:0687H         LINE#         54
  C:0688H         LINE#         56
  C:0690H         LINE#         57
  C:0690H         LINE#         58
  C:0691H         LINE#         59
  C:0699H         LINE#         60
  C:0699H         LINE#         61
  C:069AH         LINE#         64
  C:06A0H         LINE#         65
  C:06AAH         LINE#         66
  C:06AAH         LINE#         67
  C:06ADH         LINE#         68
  C:06ADH         LINE#         70
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 16


  C:06ADH         LINE#         72
  C:06ADH         LINE#         73
  C:06ADH         LINE#         74
  C:06ADH         LINE#         75
  C:06ADH         LINE#         78
  C:06B2H         LINE#         79
  C:06B2H         LINE#         80
  C:06B2H         LINE#         81
  C:06B5H         LINE#         82
  C:06B5H         LINE#         83
  C:06B5H         LINE#         84
  C:06B5H         LINE#         85
  C:06B5H         LINE#         87
  C:06BDH         LINE#         88
  C:06BDH         LINE#         89
  C:06BEH         LINE#         91
  C:06C6H         LINE#         92
  C:06C6H         LINE#         93
  C:06C7H         LINE#         94
  C:06CFH         LINE#         95
  C:06CFH         LINE#         96
  C:06D0H         LINE#         98
  C:06E0H         LINE#         99
  C:06E0H         LINE#         100
  C:06E3H         LINE#         101
  C:06E5H         LINE#         102
  C:06F0H         LINE#         103
  C:06F0H         LINE#         104
  C:06F5H         LINE#         105
  C:06F5H         LINE#         107
  C:06F5H         LINE#         109
  C:06F5H         LINE#         110
  C:06F5H         LINE#         111
  C:06F5H         LINE#         112
  C:06F5H         LINE#         115
  C:06FAH         LINE#         116
  C:06FAH         LINE#         117
  C:06FAH         LINE#         118
  C:06FDH         LINE#         119
  C:06FDH         LINE#         120
  C:06FDH         LINE#         121
  C:06FDH         LINE#         122
  C:06FDH         LINE#         123
  -------         ENDPROC       KEYPROC
  -------         ENDMOD        KEYAPP

  -------         MODULE        COMMUNICATION
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:0000H         PUBLIC        Uart
  X:003BH         PUBLIC        count
  C:09CCH         PUBLIC        _CalcChecksum
  X:003CH         PUBLIC        high_count
  D:0099H         PUBLIC        SBUF
  X:003DH         PUBLIC        duty_shadow
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 17


  C:03D3H         PUBLIC        _TxdDataProc
  X:003EH         PUBLIC        duty
  C:0A64H         PUBLIC        CommFunProc
  C:0780H         PUBLIC        CommDealResponse
  B:0098H.4       PUBLIC        REN
  C:0AD0H         SYMBOL        Com003C
  C:0AD0H         SYMBOL        L?0061
  C:0AD3H         SYMBOL        L?0062
  -------         PROC          COM003C
  -------         ENDPROC       COM003C
  -------         PROC          _CALCCHECKSUM
  X:006FH         SYMBOL        CS_Buffer
  D:0005H         SYMBOL        Len
  -------         DO            
  D:0007H         SYMBOL        CS
  D:0006H         SYMBOL        j
  -------         ENDDO         
  C:09CCH         LINE#         104
  C:09D7H         LINE#         105
  C:09D7H         LINE#         106
  C:09D9H         LINE#         107
  C:09DAH         LINE#         109
  C:09DFH         LINE#         110
  C:09DFH         LINE#         111
  C:09F4H         LINE#         112
  C:09F7H         LINE#         113
  C:09F7H         LINE#         114
  -------         ENDPROC       _CALCCHECKSUM
  -------         PROC          COMMFUNPROC
  C:0A64H         LINE#         121
  C:0A64H         LINE#         122
  C:0A64H         LINE#         123
  C:0A6CH         LINE#         124
  C:0A6CH         LINE#         125
  C:0A6DH         LINE#         153
  C:0A72H         LINE#         156
  C:0A7AH         LINE#         157
  C:0A7AH         LINE#         158
  C:0A7DH         LINE#         159
  C:0A7DH         LINE#         161
  C:0A7DH         LINE#         162
  -------         ENDPROC       COMMFUNPROC
  C:0486H         SYMBOL        L?0063
  -------         PROC          _TXDDATAPROC
  X:006EH         SYMBOL        Ack
  -------         DO            
  D:0001H         SYMBOL        checksum
  -------         ENDDO         
  C:03D3H         LINE#         169
  C:03D8H         LINE#         170
  C:03D8H         LINE#         172
  C:03D8H         LINE#         174
  C:03DDH         LINE#         175
  C:03DDH         LINE#         176
  C:03DFH         LINE#         177
  C:03DFH         LINE#         179
  C:03F4H         LINE#         180
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 18


  C:03F4H         LINE#         181
  C:03F4H         LINE#         182
  C:03F4H         LINE#         183
  C:03F4H         LINE#         184
  C:03F4H         LINE#         185
  C:03F4H         LINE#         186
  C:03F6H         LINE#         188
  C:03F6H         LINE#         189
  C:03FAH         LINE#         190
  C:03FCH         LINE#         192
  C:03FCH         LINE#         193
  C:0401H         LINE#         194
  C:0401H         LINE#         195
  C:0401H         LINE#         198
  C:0401H         LINE#         199
  C:0401H         LINE#         207
  C:0403H         LINE#         208
  C:040BH         LINE#         209
  C:040BH         LINE#         210
  C:040DH         LINE#         212
  C:040DH         LINE#         214
  C:0422H         LINE#         215
  C:0422H         LINE#         216
  C:0422H         LINE#         217
  C:0422H         LINE#         218
  C:0422H         LINE#         219
  C:0422H         LINE#         220
  C:0422H         LINE#         221
  C:0424H         LINE#         223
  C:0424H         LINE#         224
  C:0428H         LINE#         225
  C:042AH         LINE#         227
  C:042AH         LINE#         228
  C:042FH         LINE#         229
  C:042FH         LINE#         230
  C:042FH         LINE#         233
  C:0434H         LINE#         234
  C:0439H         LINE#         242
  C:043BH         LINE#         243
  C:0443H         LINE#         244
  C:0443H         LINE#         245
  C:0448H         LINE#         247
  C:0448H         LINE#         249
  C:045DH         LINE#         250
  C:045DH         LINE#         251
  C:045DH         LINE#         252
  C:045DH         LINE#         253
  C:045DH         LINE#         254
  C:045DH         LINE#         255
  C:045DH         LINE#         256
  C:045FH         LINE#         258
  C:045FH         LINE#         259
  C:0463H         LINE#         260
  C:0465H         LINE#         262
  C:0465H         LINE#         263
  C:046AH         LINE#         264
  C:046AH         LINE#         265
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 19


  C:046AH         LINE#         268
  C:046DH         LINE#         269
  C:0472H         LINE#         277
  C:0472H         LINE#         279
  C:0477H         LINE#         280
  C:047DH         LINE#         281
  C:047FH         LINE#         282
  C:0485H         LINE#         283
  -------         ENDPROC       _TXDDATAPROC
  -------         PROC          COMMDEALRESPONSE
  -------         DO            
  D:0007H         SYMBOL        rechecksum
  -------         ENDDO         
  C:0780H         LINE#         290
  C:0780H         LINE#         291
  C:0780H         LINE#         294
  C:0788H         LINE#         295
  C:0788H         LINE#         296
  C:0793H         LINE#         298
  C:079AH         LINE#         299
  C:079AH         LINE#         301
  C:07B2H         LINE#         302
  C:07B2H         LINE#         303
  C:07B2H         LINE#         304
  C:07B7H         LINE#         305
  C:07B7H         LINE#         306
  C:07B9H         LINE#         307
  C:07B9H         LINE#         308
  C:07BEH         LINE#         309
  C:07BEH         LINE#         310
  C:07C0H         LINE#         311
  C:07C0H         LINE#         312
  C:07C5H         LINE#         313
  C:07C5H         LINE#         314
  C:07C7H         LINE#         315
  C:07C7H         LINE#         316
  C:07CCH         LINE#         317
  C:07CCH         LINE#         318
  C:07CEH         LINE#         319
  C:07CEH         LINE#         320
  C:07D3H         LINE#         321
  C:07D3H         LINE#         322
  C:07D5H         LINE#         323
  C:07D5H         LINE#         324
  C:07DBH         LINE#         325
  C:07DFH         LINE#         326
  C:07DFH         LINE#         327
  C:07DFH         LINE#         328
  C:07DFH         LINE#         329
  C:07DFH         LINE#         332
  C:07E1H         LINE#         333
  C:07E3H         LINE#         335
  C:07E3H         LINE#         337
  C:07E8H         LINE#         338
  C:07E8H         LINE#         341
  C:07EDH         LINE#         342
  C:07F1H         LINE#         343
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 20


  C:07F5H         LINE#         344
  C:07F5H         LINE#         345
  -------         ENDPROC       COMMDEALRESPONSE
  -------         ENDMOD        COMMUNICATION

  -------         MODULE        LEDDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00EAH         PUBLIC        P1M0
  D:00E2H         PUBLIC        P1M1
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:0090H.2       PUBLIC        P1_2
  B:0090H.3       PUBLIC        P1_3
  B:0090H.4       PUBLIC        P1_4
  B:0090H.5       PUBLIC        P1_5
  B:0090H.6       PUBLIC        P1_6
  B:0090H.7       PUBLIC        P1_7
  C:0B0CH         PUBLIC        LED1_WorkOff
  C:0B1EH         PUBLIC        LED2_WorkOff
  C:0B30H         PUBLIC        LED3_WorkOff
  X:007BH         PUBLIC        count
  C:0B42H         PUBLIC        LED4_WorkOff
  C:0B54H         PUBLIC        LED5_WorkOff
  C:0B66H         PUBLIC        LED6_WorkOff
  X:007CH         PUBLIC        high_count
  X:007DH         PUBLIC        duty_shadow
  X:007EH         PUBLIC        duty
  C:0B03H         PUBLIC        LED1_WorkOn
  C:0B15H         PUBLIC        LED2_WorkOn
  C:0B27H         PUBLIC        LED3_WorkOn
  C:0B39H         PUBLIC        LED4_WorkOn
  C:0B4BH         PUBLIC        LED5_WorkOn
  C:0B5DH         PUBLIC        LED6_WorkOn
  C:0B92H         PUBLIC        InitAllLedDriverRam
  C:0ABEH         PUBLIC        InitAllLedOff
  -------         PROC          INITALLLEDDRIVERRAM
  C:0B92H         LINE#         49
  C:0B92H         LINE#         50
  C:0B92H         LINE#         51
  C:0B97H         LINE#         65
  -------         ENDPROC       INITALLLEDDRIVERRAM
  -------         PROC          INITALLLEDOFF
  C:0ABEH         LINE#         72
  C:0ABEH         LINE#         73
  C:0ABEH         LINE#         74
  C:0AC1H         LINE#         75
  C:0AC4H         LINE#         76
  C:0AC7H         LINE#         77
  C:0ACAH         LINE#         78
  C:0ACDH         LINE#         79
  -------         ENDPROC       INITALLLEDOFF
  -------         PROC          LED1_WORKON
  C:0B03H         LINE#         88
  C:0B03H         LINE#         89
  C:0B03H         LINE#         90
  C:0B09H         LINE#         91
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 21


  C:0B0BH         LINE#         92
  -------         ENDPROC       LED1_WORKON
  -------         PROC          LED1_WORKOFF
  C:0B0CH         LINE#         99
  C:0B0CH         LINE#         100
  C:0B0CH         LINE#         101
  C:0B12H         LINE#         102
  C:0B14H         LINE#         103
  -------         ENDPROC       LED1_WORKOFF
  -------         PROC          LED2_WORKON
  C:0B15H         LINE#         131
  C:0B15H         LINE#         132
  C:0B15H         LINE#         133
  C:0B1BH         LINE#         134
  C:0B1DH         LINE#         135
  -------         ENDPROC       LED2_WORKON
  -------         PROC          LED2_WORKOFF
  C:0B1EH         LINE#         142
  C:0B1EH         LINE#         143
  C:0B1EH         LINE#         144
  C:0B24H         LINE#         145
  C:0B26H         LINE#         146
  -------         ENDPROC       LED2_WORKOFF
  -------         PROC          LED3_WORKON
  C:0B27H         LINE#         175
  C:0B27H         LINE#         176
  C:0B27H         LINE#         177
  C:0B2DH         LINE#         178
  C:0B2FH         LINE#         179
  -------         ENDPROC       LED3_WORKON
  -------         PROC          LED3_WORKOFF
  C:0B30H         LINE#         186
  C:0B30H         LINE#         187
  C:0B30H         LINE#         188
  C:0B36H         LINE#         189
  C:0B38H         LINE#         190
  -------         ENDPROC       LED3_WORKOFF
  -------         PROC          LED4_WORKON
  C:0B39H         LINE#         218
  C:0B39H         LINE#         219
  C:0B39H         LINE#         220
  C:0B3FH         LINE#         221
  C:0B41H         LINE#         222
  -------         ENDPROC       LED4_WORKON
  -------         PROC          LED4_WORKOFF
  C:0B42H         LINE#         229
  C:0B42H         LINE#         230
  C:0B42H         LINE#         231
  C:0B48H         LINE#         232
  C:0B4AH         LINE#         233
  -------         ENDPROC       LED4_WORKOFF
  -------         PROC          LED5_WORKON
  C:0B4BH         LINE#         262
  C:0B4BH         LINE#         263
  C:0B4BH         LINE#         264
  C:0B51H         LINE#         265
  C:0B53H         LINE#         266
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 22


  -------         ENDPROC       LED5_WORKON
  -------         PROC          LED5_WORKOFF
  C:0B54H         LINE#         273
  C:0B54H         LINE#         274
  C:0B54H         LINE#         275
  C:0B5AH         LINE#         276
  C:0B5CH         LINE#         277
  -------         ENDPROC       LED5_WORKOFF
  -------         PROC          LED6_WORKON
  C:0B5DH         LINE#         305
  C:0B5DH         LINE#         306
  C:0B5DH         LINE#         307
  C:0B63H         LINE#         308
  C:0B65H         LINE#         309
  -------         ENDPROC       LED6_WORKON
  -------         PROC          LED6_WORKOFF
  C:0B66H         LINE#         316
  C:0B66H         LINE#         317
  C:0B66H         LINE#         318
  C:0B6CH         LINE#         319
  C:0B6EH         LINE#         320
  -------         ENDPROC       LED6_WORKOFF
  -------         ENDMOD        LEDDRIVER

  -------         MODULE        KEYDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00E4H         PUBLIC        P3M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:0050H         PUBLIC        KeyDrvStr
  B:00B0H.3       PUBLIC        P3_3
  B:00B0H.7       PUBLIC        P3_7
  X:005DH         PUBLIC        count
  X:005EH         PUBLIC        high_count
  C:01C6H         PUBLIC        KeyScan
  X:005FH         PUBLIC        duty_shadow
  X:0060H         PUBLIC        duty
  C:0863H         PUBLIC        InitKeyScanRam
  C:0AEDH         SYMBOL        Com002D
  C:0AEDH         SYMBOL        L?0046
  -------         PROC          COM002D
  -------         ENDPROC       COM002D
  -------         PROC          INITKEYSCANRAM
  -------         DO            
  D:0007H         SYMBOL        i
  -------         ENDDO         
  C:0863H         LINE#         48
  C:0863H         LINE#         49
  C:0863H         LINE#         52
  C:0868H         LINE#         53
  C:0873H         LINE#         54
  C:0873H         LINE#         55
  C:087FH         LINE#         56
  C:088BH         LINE#         57
  C:0897H         LINE#         58
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 23


  C:08A3H         LINE#         59
  C:08AFH         LINE#         60
  C:08BBH         LINE#         61
  C:08BEH         LINE#         62
  -------         ENDPROC       INITKEYSCANRAM
  -------         PROC          KEYSCAN
  -------         DO            
  X:006EH         SYMBOL        tempbuff
  -------         ENDDO         
  C:01C6H         LINE#         69
  C:01C6H         LINE#         70
  C:01C6H         LINE#         73
  C:01D1H         LINE#         74
  C:01D1H         LINE#         75
  C:01D2H         LINE#         77
  C:01D8H         LINE#         78
  C:01DEH         LINE#         79
  C:01E0H         LINE#         80
  C:01E2H         LINE#         81
  C:01E3H         LINE#         82
  C:01E4H         LINE#         83
  C:01E5H         LINE#         84
  C:01E6H         LINE#         85
  C:01E7H         LINE#         86
  C:01E8H         LINE#         87
  C:01E9H         LINE#         88
  C:01EAH         LINE#         89
  C:01EBH         LINE#         90
  C:01ECH         LINE#         91
  C:01EFH         LINE#         92
  C:01EFH         LINE#         93
  C:01F4H         LINE#         94
  C:01F6H         LINE#         96
  C:01F6H         LINE#         97
  C:01FBH         LINE#         98
  C:01FBH         LINE#         99
  C:01FEH         LINE#         100
  C:01FEH         LINE#         101
  C:0204H         LINE#         102
  C:0206H         LINE#         104
  C:0206H         LINE#         105
  C:020BH         LINE#         106
  C:020BH         LINE#         110
  C:0217H         LINE#         111
  C:0217H         LINE#         112
  C:0219H         LINE#         113
  C:021EH         LINE#         114
  C:0220H         LINE#         116
  C:0220H         LINE#         117
  C:0223H         LINE#         118
  C:0228H         LINE#         119
  C:0228H         LINE#         120
  C:022AH         LINE#         122
  C:0237H         LINE#         123
  C:0237H         LINE#         124
  C:023CH         LINE#         126
  C:023FH         LINE#         127
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 24


  C:023FH         LINE#         128
  C:0245H         LINE#         129
  C:0247H         LINE#         130
  C:024EH         LINE#         131
  C:024EH         LINE#         132
  C:0251H         LINE#         133
  C:0257H         LINE#         134
  C:025AH         LINE#         135
  C:025CH         LINE#         137
  C:025CH         LINE#         138
  C:025FH         LINE#         139
  C:025FH         LINE#         140
  C:0261H         LINE#         142
  C:0261H         LINE#         143
  C:0265H         LINE#         144
  C:0265H         LINE#         145
  C:026BH         LINE#         146
  C:026DH         LINE#         147
  C:0270H         LINE#         148
  C:0270H         LINE#         149
  C:0276H         LINE#         150
  C:0278H         LINE#         152
  C:0278H         LINE#         153
  C:027DH         LINE#         154
  C:0281H         LINE#         155
  C:0285H         LINE#         156
  C:0289H         LINE#         157
  C:0289H         LINE#         158
  C:0289H         LINE#         159
  C:0289H         LINE#         160
  C:0289H         LINE#         163
  C:0295H         LINE#         164
  C:0295H         LINE#         165
  C:0297H         LINE#         166
  C:029CH         LINE#         167
  C:029DH         LINE#         169
  C:029DH         LINE#         170
  C:02A0H         LINE#         171
  C:02A5H         LINE#         172
  C:02A5H         LINE#         173
  C:02A7H         LINE#         175
  C:02B4H         LINE#         176
  C:02B4H         LINE#         177
  C:02B9H         LINE#         179
  C:02BCH         LINE#         180
  C:02BCH         LINE#         181
  C:02C2H         LINE#         182
  C:02C3H         LINE#         183
  C:02CAH         LINE#         184
  C:02CAH         LINE#         185
  C:02CDH         LINE#         186
  C:02D3H         LINE#         187
  C:02D6H         LINE#         188
  C:02D8H         LINE#         190
  C:02D8H         LINE#         191
  C:02DBH         LINE#         192
  C:02DBH         LINE#         193
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 25


  C:02DCH         LINE#         195
  C:02DCH         LINE#         196
  C:02E0H         LINE#         197
  C:02E0H         LINE#         198
  C:02E6H         LINE#         199
  C:02E7H         LINE#         200
  C:02EAH         LINE#         201
  C:02EAH         LINE#         202
  C:02F0H         LINE#         203
  C:02F1H         LINE#         205
  C:02F1H         LINE#         206
  C:02F6H         LINE#         207
  C:02FAH         LINE#         208
  C:02FEH         LINE#         209
  C:0302H         LINE#         210
  C:0302H         LINE#         211
  C:0302H         LINE#         212
  C:0302H         LINE#         213
  C:0302H         LINE#         214
  C:0302H         LINE#         215
  -------         ENDPROC       KEYSCAN
  -------         ENDMOD        KEYDRIVER

  -------         MODULE        UARTDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00E4H         PUBLIC        P3M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00A8H         PUBLIC        IEN0
  C:0952H         PUBLIC        _Uart0CalBaudrate
  B:0098H.0       PUBLIC        RI
  D:00CBH         PUBLIC        RCAP2H
  D:00CAH         PUBLIC        RCAP2L
  X:007FH         PUBLIC        count
  X:0080H         PUBLIC        high_count
  D:0087H         PUBLIC        PCON
  X:0081H         PUBLIC        duty_shadow
  D:0098H         PUBLIC        SCON
  X:0082H         PUBLIC        duty
  D:00CDH         PUBLIC        TH2
  D:00CCH         PUBLIC        TL2
  B:00C8H.2       PUBLIC        TR2
  C:09F8H         PUBLIC        Uart0Init
  B:0098H.4       PUBLIC        REN
  D:00C9H         PUBLIC        T2MOD
  D:00C8H         PUBLIC        T2CON
  D:009BH         PUBLIC        SADEN
  D:009AH         PUBLIC        SADDR
  -------         PROC          UART0INIT
  C:09F8H         LINE#         49
  C:09F8H         LINE#         50
  C:09F8H         LINE#         51
  C:09FBH         LINE#         52
  C:09FEH         LINE#         53
  C:0A01H         LINE#         59
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 26


  C:0A04H         LINE#         60
  C:0A07H         LINE#         61
  C:0A0AH         LINE#         63
  C:0A0DH         LINE#         64
  C:0A0FH         LINE#         66
  C:0A16H         LINE#         67
  C:0A19H         LINE#         68
  C:0A1BH         LINE#         69
  C:0A1DH         LINE#         70
  C:0A20H         LINE#         71
  -------         ENDPROC       UART0INIT
  -------         PROC          _UART0CALBAUDRATE
  D:0006H         SYMBOL        baudratepar
  -------         DO            
  X:006EH         SYMBOL        CalBaudRateTemp
  -------         ENDDO         
  C:0952H         LINE#         80
  C:0952H         LINE#         81
  C:0952H         LINE#         84
  C:0970H         LINE#         89
  C:0973H         LINE#         90
  C:0976H         LINE#         91
  C:0979H         LINE#         92
  C:097CH         LINE#         93
  C:097FH         LINE#         94
  C:0982H         LINE#         95
  C:0985H         LINE#         96
  C:0987H         LINE#         97
  C:0989H         LINE#         99
  C:098CH         LINE#         100
  C:098FH         LINE#         101
  C:0991H         LINE#         102
  -------         ENDPROC       _UART0CALBAUDRATE
  -------         ENDMOD        UARTDRIVER

  -------         MODULE        PWMDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  C:0AABH         PUBLIC        Timer1_Init
  X:0083H         PUBLIC        count
  X:0084H         PUBLIC        high_count
  X:0085H         PUBLIC        duty_shadow
  D:0089H         PUBLIC        TMOD
  X:0086H         PUBLIC        duty
  B:00A8H.3       PUBLIC        ET1
  D:008DH         PUBLIC        TH1
  D:008BH         PUBLIC        TL1
  B:0088H.6       PUBLIC        TR1
  -------         PROC          TIMER1_INIT
  C:0AABH         LINE#         9
  C:0AABH         LINE#         10
  C:0AAEH         LINE#         11
  C:0AB1H         LINE#         12
  C:0AB4H         LINE#         13
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 27


  C:0AB7H         LINE#         14
  C:0AB9H         LINE#         15
  C:0ABBH         LINE#         16
  C:0ABDH         LINE#         17
  -------         ENDPROC       TIMER1_INIT
  -------         ENDMOD        PWMDRIVER

  -------         MODULE        ?C?CLDOPTR
  C:0076H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?UIDIV
  C:00A3H         PUBLIC        ?C?UIDIV
  -------         ENDMOD        ?C?UIDIV

  -------         MODULE        ?C?SLDIV
  C:090EH         PUBLIC        ?C?SLDIV
  -------         ENDMOD        ?C?SLDIV

  -------         MODULE        ?C?ULDIV
  C:0134H         PUBLIC        ?C?ULDIV
  -------         ENDMOD        ?C?ULDIV

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY
    MODULE:  .\output\Mcu.obj (MCU)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY_SHADOW
    MODULE:  .\output\Mcu.obj (MCU)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  HIGH_COUNT
    MODULE:  .\output\Mcu.obj (MCU)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  COUNT
    MODULE:  .\output\Mcu.obj (MCU)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY
    MODULE:  .\output\Interrupt.obj (INTERRUPT)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY_SHADOW
    MODULE:  .\output\Interrupt.obj (INTERRUPT)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  HIGH_COUNT
    MODULE:  .\output\Interrupt.obj (INTERRUPT)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  COUNT
    MODULE:  .\output\Interrupt.obj (INTERRUPT)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 28


    MODULE:  .\output\LedApp.obj (LEDAPP)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY_SHADOW
    MODULE:  .\output\LedApp.obj (LEDAPP)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  HIGH_COUNT
    MODULE:  .\output\LedApp.obj (LEDAPP)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  COUNT
    MODULE:  .\output\LedApp.obj (LEDAPP)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY
    MODULE:  .\output\KeyApp.obj (KEYAPP)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY_SHADOW
    MODULE:  .\output\KeyApp.obj (KEYAPP)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  HIGH_COUNT
    MODULE:  .\output\KeyApp.obj (KEYAPP)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  COUNT
    MODULE:  .\output\KeyApp.obj (KEYAPP)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY
    MODULE:  .\output\Communication.obj (COMMUNICATION)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY_SHADOW
    MODULE:  .\output\Communication.obj (COMMUNICATION)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  HIGH_COUNT
    MODULE:  .\output\Communication.obj (COMMUNICATION)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  COUNT
    MODULE:  .\output\Communication.obj (COMMUNICATION)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY
    MODULE:  .\output\LedDriver.obj (LEDDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY_SHADOW
    MODULE:  .\output\LedDriver.obj (LEDDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  HIGH_COUNT
    MODULE:  .\output\LedDriver.obj (LEDDRIVER)
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 29



*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  COUNT
    MODULE:  .\output\LedDriver.obj (LEDDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY
    MODULE:  .\output\KeyDriver.obj (KEYDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY_SHADOW
    MODULE:  .\output\KeyDriver.obj (KEYDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  HIGH_COUNT
    MODULE:  .\output\KeyDriver.obj (KEYDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  COUNT
    MODULE:  .\output\KeyDriver.obj (KEYDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY
    MODULE:  .\output\UartDriver.obj (UARTDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY_SHADOW
    MODULE:  .\output\UartDriver.obj (UARTDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  HIGH_COUNT
    MODULE:  .\output\UartDriver.obj (UARTDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  COUNT
    MODULE:  .\output\UartDriver.obj (UARTDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY
    MODULE:  .\output\PwmDriver.obj (PWMDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  DUTY_SHADOW
    MODULE:  .\output\PwmDriver.obj (PWMDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  HIGH_COUNT
    MODULE:  .\output\PwmDriver.obj (PWMDRIVER)

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  COUNT
    MODULE:  .\output\PwmDriver.obj (PWMDRIVER)

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?TIMER1_INIT?PWMDRIVER

Program Size: data=9.0 xdata=135 code=2967
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:50:43  PAGE 30


LINK/LOCATE RUN COMPLETE.  1 WARNING(S),  36 ERROR(S)
