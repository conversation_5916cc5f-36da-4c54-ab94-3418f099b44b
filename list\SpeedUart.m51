BL51 BANKED LINKER/LOCATER V6.22.2.0                                                    06/24/2025  17:57:20  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22.2.0, INVOKED BY:
D:\KEIL_V5\C51\BIN\BL51.EXE .\output\STARTUP.obj, .\output\Main.obj, .\output\Mcu.obj, .\output\Interrupt.obj, .\output\
>> LedApp.obj, .\output\KeyApp.obj, .\output\Communication.obj, .\output\LedDriver.obj, .\output\KeyDriver.obj, .\output
>> \UartDriver.obj, .\output\PwmDriver.obj TO .\output\SpeedUart PRINT (.\list\SpeedUart.m51) RAMSIZE (256)


MEMORY MODEL: LARGE


INPUT MODULES INCLUDED:
  .\output\STARTUP.obj (?C_STARTUP)
  .\output\Main.obj (MAIN)
  .\output\Mcu.obj (MCU)
  .\output\Interrupt.obj (INTERRUPT)
  .\output\LedApp.obj (LEDAPP)
  .\output\KeyApp.obj (KEYAPP)
  .\output\Communication.obj (COMMUNICATION)
  .\output\LedDriver.obj (LEDDRIVER)
  .\output\KeyDriver.obj (KEYDRIVER)
  .\output\UartDriver.obj (UARTDRIVER)
  .\output\PwmDriver.obj (PWMDRIVER)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C_INIT)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?CLDOPTR)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?UIDIV)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?SLDIV)
  D:\KEIL_V5\C51\LIB\C51L.LIB (?C?ULDIV)


LINK MAP OF MODULE:  .\output\SpeedUart (?C_STARTUP)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            IDATA   0008H     0001H     UNIT         ?STACK

            * * * * * * *  X D A T A   M E M O R Y  * * * * * * *
            XDATA   0000H     003BH     UNIT         ?XD?COMMUNICATION
            XDATA   003BH     0011H     UNIT         ?XD?INTERRUPT
            XDATA   004CH     000DH     UNIT         ?XD?KEYDRIVER
            XDATA   0059H     0005H     UNIT         _XDATA_GROUP_
            XDATA   005EH     0003H     UNIT         ?XD?LEDAPP
            XDATA   0061H     0002H     UNIT         ?XD?KEYAPP

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0003H     ABSOLUTE     
            CODE    0006H     0005H     UNIT         ?PR?EX0_INT?INTERRUPT
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0005H     UNIT         ?PR?EX1_INT?INTERRUPT
            CODE    0013H     0003H     ABSOLUTE     
            CODE    0016H     0005H     UNIT         ?PR?TIMER2_INT?INTERRUPT
            CODE    001BH     0003H     ABSOLUTE     
            CODE    001EH     0004H     UNIT         ?PR?SCM_INT?INTERRUPT
                    0022H     0001H                  *** GAP ***
            CODE    0023H     0003H     ABSOLUTE     
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 2


            CODE    0026H     0004H     UNIT         ?PR?PWM_INT?INTERRUPT
                    002AH     0001H                  *** GAP ***
            CODE    002BH     0003H     ABSOLUTE     
            CODE    002EH     001AH     UNIT         ?PR?INITT0?MCU
            CODE    0048H     0003H     UNIT         ?PR?INITCPUACSET?MCU
            CODE    004BH     0003H     ABSOLUTE     
            CODE    004EH     000DH     UNIT         ?PR?MONITORCPUTIMER?MCU
            CODE    005BH     0003H     ABSOLUTE     
            CODE    005EH     0004H     UNIT         ?PR?ELPD_INT?INTERRUPT
                    0062H     0001H                  *** GAP ***
            CODE    0063H     0003H     ABSOLUTE     
            CODE    0066H     000CH     UNIT         ?PR?INITINTERRUPTRAM?INTERRUPT
                    0072H     0001H                  *** GAP ***
            CODE    0073H     0003H     ABSOLUTE     
            CODE    0076H     0150H     UNIT         ?C?LIB_CODE
            CODE    01C6H     013DH     UNIT         ?PR?KEYSCAN?KEYDRIVER
            CODE    0303H     00D0H     UNIT         ?PR?EUART0_INT?INTERRUPT
            CODE    03D3H     00BFH     UNIT         ?PR?_TXDDATAPROC?COMMUNICATION
            CODE    0492H     00BCH     UNIT         ?PR?TIMER0_INT?INTERRUPT
            CODE    054EH     008CH     UNIT         ?C_C51STARTUP
            CODE    05DAH     008BH     UNIT         ?PR?KEYPROC?KEYAPP
            CODE    0665H     0076H     UNIT         ?PR?LEDAPP?LEDAPP
            CODE    06DBH     0076H     UNIT         ?PR?COMMDEALRESPONSE?COMMUNICATION
            CODE    0751H     006DH     UNIT         ?PR?TIMER1_INT?INTERRUPT
            CODE    07BEH     005CH     UNIT         ?PR?INITKEYSCANRAM?KEYDRIVER
            CODE    081AH     0052H     UNIT         ?PR?MAIN?MAIN
            CODE    086CH     0044H     UNIT         ?C?LDIV
            CODE    08B0H     0040H     UNIT         ?PR?_UART0CALBAUDRATE?UARTDRIVER
            CODE    08F0H     003AH     UNIT         ?PR?INTERRUPT
            CODE    092AH     002CH     UNIT         ?PR?_CALCCHECKSUM?COMMUNICATION
            CODE    0956H     0029H     UNIT         ?PR?UART0INIT?UARTDRIVER
            CODE    097FH     0022H     UNIT         ?PR?KEYAPP
            CODE    09A1H     0021H     UNIT         ?PR?_DELAYNMS?MCU
            CODE    09C2H     001AH     UNIT         ?PR?COMMFUNPROC?COMMUNICATION
            CODE    09DCH     0019H     UNIT         ?PR?INITPORT?MCU
            CODE    09F5H     0015H     UNIT         ?C_INITSEG
            CODE    0A0AH     0014H     UNIT         ?PR?INITT1?MCU
            CODE    0A1EH     0013H     UNIT         ?PR?TIMER1_INIT?PWMDRIVER
            CODE    0A31H     0012H     UNIT         ?PR?INITALLLEDOFF?LEDDRIVER
            CODE    0A43H     000FH     UNIT         ?PR?COMMUNICATION
            CODE    0A52H     000EH     UNIT         ?PR?INITSYS?MCU
            CODE    0A60H     000CH     UNIT         ?PR?KEYDRIVER
            CODE    0A6CH     000AH     UNIT         ?PR?INITINTERRUPTPRIORITYLEVEL?MCU
            CODE    0A76H     0009H     UNIT         ?PR?LED1_WORKON?LEDDRIVER
            CODE    0A7FH     0009H     UNIT         ?PR?LED1_WORKOFF?LEDDRIVER
            CODE    0A88H     0009H     UNIT         ?PR?LED2_WORKON?LEDDRIVER
            CODE    0A91H     0009H     UNIT         ?PR?LED2_WORKOFF?LEDDRIVER
            CODE    0A9AH     0009H     UNIT         ?PR?LED3_WORKON?LEDDRIVER
            CODE    0AA3H     0009H     UNIT         ?PR?LED3_WORKOFF?LEDDRIVER
            CODE    0AACH     0009H     UNIT         ?PR?LED4_WORKON?LEDDRIVER
            CODE    0AB5H     0009H     UNIT         ?PR?LED4_WORKOFF?LEDDRIVER
            CODE    0ABEH     0009H     UNIT         ?PR?LED5_WORKON?LEDDRIVER
            CODE    0AC7H     0009H     UNIT         ?PR?LED5_WORKOFF?LEDDRIVER
            CODE    0AD0H     0009H     UNIT         ?PR?LED6_WORKON?LEDDRIVER
            CODE    0AD9H     0009H     UNIT         ?PR?LED6_WORKOFF?LEDDRIVER
            CODE    0AE2H     0008H     UNIT         ?PR?INITPWM?MCU
            CODE    0AEAH     0007H     UNIT         ?PR?INITINT1?MCU
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 3


            CODE    0AF1H     0007H     UNIT         ?PR?INITINT2?MCU
            CODE    0AF8H     0007H     UNIT         ?PR?EX2_INT?INTERRUPT
            CODE    0AFFH     0006H     UNIT         ?PR?INITKEYPROCRAM?KEYAPP
            CODE    0B05H     0006H     UNIT         ?PR?INITALLLEDDRIVERRAM?LEDDRIVER
            CODE    0B0BH     0003H     UNIT         ?PR?INITALLLEDRAMAPP?LEDAPP



OVERLAY MAP OF MODULE:   .\output\SpeedUart (?C_STARTUP)


SEGMENT                                      XDATA_GROUP
  +--> CALLED SEGMENT                      START    LENGTH
----------------------------------------------------------
?C_C51STARTUP                              -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                              -----    -----
  +--> ?PR?INITSYS?MCU
  +--> ?PR?_DELAYNMS?MCU
  +--> ?PR?INITPORT?MCU
  +--> ?PR?INITKEYSCANRAM?KEYDRIVER
  +--> ?PR?INITKEYPROCRAM?KEYAPP
  +--> ?PR?INITINTERRUPTRAM?INTERRUPT
  +--> ?PR?INITALLLEDOFF?LEDDRIVER
  +--> ?PR?INITALLLEDRAMAPP?LEDAPP
  +--> ?PR?INITT0?MCU
  +--> ?PR?INITT1?MCU
  +--> ?PR?INITINT1?MCU
  +--> ?PR?INITINT2?MCU
  +--> ?PR?INITPWM?MCU
  +--> ?PR?UART0INIT?UARTDRIVER
  +--> ?PR?MONITORCPUTIMER?MCU
  +--> ?PR?INITCPUACSET?MCU
  +--> ?PR?KEYSCAN?KEYDRIVER
  +--> ?PR?KEYPROC?KEYAPP
  +--> ?PR?LEDAPP?LEDAPP
  +--> ?PR?COMMFUNPROC?COMMUNICATION
  +--> ?PR?TIMER1_INIT?PWMDRIVER

?PR?INITSYS?MCU                            -----    -----
  +--> ?PR?INITINTERRUPTPRIORITYLEVEL?MCU

?PR?INITINTERRUPTRAM?INTERRUPT             -----    -----
  +--> ?PR?INTERRUPT

?PR?INITALLLEDOFF?LEDDRIVER                -----    -----
  +--> ?PR?LED1_WORKOFF?LEDDRIVER
  +--> ?PR?LED2_WORKOFF?LEDDRIVER
  +--> ?PR?LED3_WORKOFF?LEDDRIVER
  +--> ?PR?LED4_WORKOFF?LEDDRIVER
  +--> ?PR?LED5_WORKOFF?LEDDRIVER
  +--> ?PR?LED6_WORKOFF?LEDDRIVER

?PR?INITALLLEDRAMAPP?LEDAPP                -----    -----
  +--> ?PR?INITALLLEDDRIVERRAM?LEDDRIVER
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 4



?PR?UART0INIT?UARTDRIVER                   -----    -----
  +--> ?PR?_UART0CALBAUDRATE?UARTDRIVER

?PR?_UART0CALBAUDRATE?UARTDRIVER           0059H    0002H

?PR?KEYSCAN?KEYDRIVER                      0059H    0002H
  +--> ?PR?KEYDRIVER

?PR?KEYPROC?KEYAPP                         -----    -----
  +--> ?PR?KEYAPP
  +--> ?PR?_TXDDATAPROC?COMMUNICATION

?PR?_TXDDATAPROC?COMMUNICATION             0059H    0001H
  +--> ?PR?COMMUNICATION
  +--> ?PR?_CALCCHECKSUM?COMMUNICATION

?PR?_CALCCHECKSUM?COMMUNICATION            005AH    0003H

?PR?LEDAPP?LEDAPP                          -----    -----
  +--> ?PR?INITALLLEDOFF?LEDDRIVER
  +--> ?PR?LED1_WORKON?LEDDRIVER
  +--> ?PR?LED2_WORKON?LEDDRIVER
  +--> ?PR?LED3_WORKON?LEDDRIVER
  +--> ?PR?LED4_WORKON?LEDDRIVER
  +--> ?PR?LED5_WORKON?LEDDRIVER
  +--> ?PR?LED6_WORKON?LEDDRIVER

?PR?COMMFUNPROC?COMMUNICATION              -----    -----
  +--> ?PR?_TXDDATAPROC?COMMUNICATION
  +--> ?PR?COMMDEALRESPONSE?COMMUNICATION

?PR?COMMDEALRESPONSE?COMMUNICATION         -----    -----
  +--> ?PR?_CALCCHECKSUM?COMMUNICATION
  +--> ?PR?_TXDDATAPROC?COMMUNICATION

*** NEW ROOT ***************************************************

?PR?TIMER0_INT?INTERRUPT                   -----    -----
  +--> ?PR?INTERRUPT

*** NEW ROOT ***************************************************

?PR?EUART0_INT?INTERRUPT                   005DH    0001H
  +--> ?PR?INTERRUPT



SYMBOL TABLE OF MODULE:  .\output\SpeedUart (?C_STARTUP)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        ?C_STARTUP
  C:054EH         SEGMENT       ?C_C51STARTUP
  I:0008H         SEGMENT       ?STACK
  C:0000H         PUBLIC        ?C_STARTUP
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 5


  D:00E0H         SYMBOL        ACC
  D:00F0H         SYMBOL        B
  N:3026H         SYMBOL        CODE_SIZE
  D:0083H         SYMBOL        DPH
  D:0082H         SYMBOL        DPL
  N:0F9AH         SYMBOL        FILLING_A5_NUM
  C:055AH         SYMBOL        FILL_CODE
  N:0000H         SYMBOL        IBPSTACK
  N:0100H         SYMBOL        IBPSTACKTOP
  N:0080H         SYMBOL        IDATALEN
  C:0551H         SYMBOL        IDATALOOP
  N:0000H         SYMBOL        PBPSTACK
  N:0000H         SYMBOL        PBPSTACKTOP
  N:0000H         SYMBOL        PDATALEN
  N:0000H         SYMBOL        PDATASTART
  N:0000H         SYMBOL        PPAGE
  N:0000H         SYMBOL        PPAGEENABLE
  D:00A0H         SYMBOL        PPAGE_SFR
  N:3FC0H         SYMBOL        ROM_SIZE
  D:0081H         SYMBOL        SP
  C:054EH         SYMBOL        STARTUP1
  N:0000H         SYMBOL        XBPSTACK
  N:0000H         SYMBOL        XBPSTACKTOP
  N:0000H         SYMBOL        XDATALEN
  N:0000H         SYMBOL        XDATASTART
  C:0000H         LINE#         107
  C:054EH         LINE#         114
  C:0550H         LINE#         115
  C:0551H         LINE#         116
  C:0552H         LINE#         117
  C:0554H         LINE#         166
  C:0557H         LINE#         170
  -------         ENDMOD        ?C_STARTUP

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IEN0
  D:00A9H         PUBLIC        IEN1
  C:081AH         PUBLIC        main
  -------         PROC          MAIN
  C:081AH         LINE#         50
  C:081AH         LINE#         51
  C:081AH         LINE#         52
  C:081CH         LINE#         53
  C:081FH         LINE#         54
  C:0826H         LINE#         55
  C:0829H         LINE#         56
  C:082CH         LINE#         57
  C:082FH         LINE#         58
  C:0832H         LINE#         59
  C:0835H         LINE#         60
  C:0838H         LINE#         61
  C:083BH         LINE#         62
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 6


  C:083EH         LINE#         63
  C:0841H         LINE#         64
  C:0844H         LINE#         65
  C:0847H         LINE#         66
  C:084AH         LINE#         67
  C:084DH         LINE#         68
  C:0850H         LINE#         69
  C:0852H         LINE#         80
  C:0852H         LINE#         81
  C:0852H         LINE#         82
  C:0855H         LINE#         83
  C:0858H         LINE#         84
  C:085BH         LINE#         85
  C:085EH         LINE#         86
  C:0861H         LINE#         87
  C:0864H         LINE#         88
  C:0867H         LINE#         89
  C:086AH         LINE#         90
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        MCU
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00EAH         PUBLIC        P1M0
  D:00E2H         PUBLIC        P1M1
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00EDH         PUBLIC        P4M0
  D:00E4H         PUBLIC        P3M1
  D:00E5H         PUBLIC        P4M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  D:00A9H         PUBLIC        IEN1
  D:00B4H         PUBLIC        IPH0
  D:00B5H         PUBLIC        IPH1
  D:00E8H         PUBLIC        EXF0
  D:00B8H         PUBLIC        IPL0
  D:00B9H         PUBLIC        IPL1
  C:002EH         PUBLIC        InitT0
  C:004EH         PUBLIC        MonitorCpuTimer
  C:0A0AH         PUBLIC        InitT1
  C:09A1H         PUBLIC        _DelayNms
  C:0AEAH         PUBLIC        InitINT1
  C:0AF1H         PUBLIC        InitINT2
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:00B1H         PUBLIC        RSTSTAT
  D:00D3H         PUBLIC        PWMD
  D:00B2H         PUBLIC        CLKCON
  C:09DCH         PUBLIC        InitPort
  B:0088H.3       PUBLIC        IE1
  D:00B3H         PUBLIC        LPDCON
  D:00D2H         PUBLIC        PWMP
  B:0088H.5       PUBLIC        TF0
  B:0088H.7       PUBLIC        TF1
  C:0AE2H         PUBLIC        InitPwm
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 7


  D:008CH         PUBLIC        TH0
  D:008DH         PUBLIC        TH1
  B:0088H.2       PUBLIC        IT1
  B:00A8H.2       PUBLIC        EX1
  C:0A6CH         PUBLIC        InitInterruptPriorityLevel
  D:008AH         PUBLIC        TL0
  D:008BH         PUBLIC        TL1
  B:0088H.4       PUBLIC        TR0
  D:00D1H         PUBLIC        PWMCON
  C:0A52H         PUBLIC        InitSys
  B:0088H.6       PUBLIC        TR1
  D:00CEH         PUBLIC        TCON1
  C:0048H         PUBLIC        InitCpuACSet
  -------         PROC          INITSYS
  C:0A52H         LINE#         50
  C:0A52H         LINE#         51
  C:0A52H         LINE#         52
  C:0A54H         LINE#         53
  C:0A57H         LINE#         54
  C:0A59H         LINE#         55
  C:0A5CH         LINE#         56
  C:0A5FH         LINE#         57
  -------         ENDPROC       INITSYS
  -------         PROC          INITCPUACSET
  C:0048H         LINE#         64
  C:0048H         LINE#         65
  C:0048H         LINE#         66
  C:004AH         LINE#         67
  -------         ENDPROC       INITCPUACSET
  -------         PROC          INITPORT
  C:09DCH         LINE#         74
  C:09DCH         LINE#         75
  C:09DCH         LINE#         76
  C:09DFH         LINE#         77
  C:09E2H         LINE#         78
  C:09E4H         LINE#         80
  C:09E7H         LINE#         81
  C:09EAH         LINE#         83
  C:09ECH         LINE#         84
  C:09EFH         LINE#         86
  C:09F1H         LINE#         87
  C:09F4H         LINE#         88
  -------         ENDPROC       INITPORT
  -------         PROC          INITINTERRUPTPRIORITYLEVEL
  C:0A6CH         LINE#         96
  C:0A6CH         LINE#         97
  C:0A6CH         LINE#         98
  C:0A6FH         LINE#         99
  C:0A71H         LINE#         100
  C:0A73H         LINE#         101
  C:0A75H         LINE#         102
  -------         ENDPROC       INITINTERRUPTPRIORITYLEVEL
  -------         PROC          INITT0
  C:002EH         LINE#         109
  C:002EH         LINE#         110
  C:002EH         LINE#         111
  C:0031H         LINE#         113
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 8


  C:0034H         LINE#         114
  C:0037H         LINE#         116
  C:003AH         LINE#         117
  C:003DH         LINE#         119
  C:0040H         LINE#         120
  C:0043H         LINE#         121
  C:0045H         LINE#         122
  C:0047H         LINE#         123
  -------         ENDPROC       INITT0
  -------         PROC          INITT1
  C:0A0AH         LINE#         131
  C:0A0AH         LINE#         132
  C:0A0AH         LINE#         133
  C:0A0DH         LINE#         135
  C:0A10H         LINE#         136
  C:0A10H         LINE#         139
  C:0A13H         LINE#         140
  C:0A13H         LINE#         143
  C:0A16H         LINE#         144
  C:0A19H         LINE#         145
  C:0A1BH         LINE#         146
  C:0A1DH         LINE#         147
  -------         ENDPROC       INITT1
  -------         PROC          INITINT1
  C:0AEAH         LINE#         155
  C:0AEAH         LINE#         156
  C:0AEAH         LINE#         157
  C:0AECH         LINE#         158
  C:0AEEH         LINE#         159
  C:0AF0H         LINE#         160
  -------         ENDPROC       INITINT1
  -------         PROC          INITINT2
  C:0AF1H         LINE#         167
  C:0AF1H         LINE#         168
  C:0AF1H         LINE#         169
  C:0AF4H         LINE#         170
  C:0AF7H         LINE#         171
  -------         ENDPROC       INITINT2
  -------         PROC          INITPWM
  C:0AE2H         LINE#         179
  C:0AE2H         LINE#         180
  C:0AE2H         LINE#         185
  C:0AE5H         LINE#         186
  C:0AE7H         LINE#         187
  C:0AE9H         LINE#         188
  -------         ENDPROC       INITPWM
  -------         PROC          MONITORCPUTIMER
  C:004EH         LINE#         196
  C:004EH         LINE#         197
  C:004EH         LINE#         198
  C:0056H         LINE#         199
  C:0056H         LINE#         200
  C:0057H         LINE#         202
  C:005AH         LINE#         203
  C:005AH         LINE#         204
  -------         ENDPROC       MONITORCPUTIMER
  -------         PROC          _DELAYNMS
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 9


  D:0006H         SYMBOL        num
  -------         DO            
  D:0004H         SYMBOL        i
  D:0002H         SYMBOL        j
  -------         ENDDO         
  C:09A1H         LINE#         212
  C:09A1H         LINE#         213
  C:09A1H         LINE#         216
  C:09ABH         LINE#         217
  C:09ABH         LINE#         218
  C:09AEH         LINE#         219
  C:09AEH         LINE#         220
  C:09AFH         LINE#         221
  C:09BAH         LINE#         222
  C:09C1H         LINE#         223
  -------         ENDPROC       _DELAYNMS
  -------         ENDMOD        MCU

  -------         MODULE        INTERRUPT
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:0303H         PUBLIC        EUART0_INT
  C:0492H         PUBLIC        Timer0_INT
  D:0090H         PUBLIC        P1
  C:0751H         PUBLIC        Timer1_INT
  C:0016H         PUBLIC        Timer2_INT
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:0066H         PUBLIC        InitInterruptRam
  D:00A9H         PUBLIC        IEN1
  D:00E8H         PUBLIC        EXF0
  B:00C8H.6       PUBLIC        EXF2
  C:0006H         PUBLIC        EX0_INT
  C:000EH         PUBLIC        EX1_INT
  B:0098H.0       PUBLIC        RI
  C:0AF8H         PUBLIC        EX2_INT
  B:0098H.1       PUBLIC        TI
  X:003BH         PUBLIC        count
  X:003CH         PUBLIC        high_count
  C:001EH         PUBLIC        SCM_INT
  D:0099H         PUBLIC        SBUF
  X:003DH         PUBLIC        duty_shadow
  D:00B2H         PUBLIC        CLKCON
  B:0088H.1       PUBLIC        IE0
  B:0088H.3       PUBLIC        IE1
  C:0026H         PUBLIC        PWM_INT
  D:00B3H         PUBLIC        LPDCON
  X:003EH         PUBLIC        duty
  X:003FH         PUBLIC        TimeFlagStr
  B:0088H.5       PUBLIC        TF0
  B:0088H.7       PUBLIC        TF1
  B:00C8H.7       PUBLIC        TF2
  D:008CH         PUBLIC        TH0
  B:00A8H.0       PUBLIC        EX0
  D:008DH         PUBLIC        TH1
  B:00A8H.2       PUBLIC        EX1
  D:008AH         PUBLIC        TL0
  D:008BH         PUBLIC        TL1
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 10


  B:0088H.4       PUBLIC        TR0
  D:00D1H         PUBLIC        PWMCON
  B:0088H.6       PUBLIC        TR1
  B:0098H.4       PUBLIC        REN
  B:00B0H.0       PUBLIC        PWM_IN
  C:005EH         PUBLIC        ELPD_INT
  C:08F0H         SYMBOL        Com0028
  C:08F0H         SYMBOL        L?0041
  C:08FCH         SYMBOL        L?0042
  C:090AH         SYMBOL        L?0043
  C:0912H         SYMBOL        L?0044
  C:0915H         SYMBOL        L?0045
  C:091FH         SYMBOL        L?0046
  -------         PROC          COM0028
  -------         ENDPROC       COM0028
  -------         PROC          INITINTERRUPTRAM
  C:0066H         LINE#         52
  C:0066H         LINE#         53
  C:0066H         LINE#         54
  C:006AH         LINE#         56
  C:006AH         LINE#         57
  C:006AH         LINE#         58
  C:006AH         LINE#         60
  C:006AH         LINE#         61
  C:006DH         LINE#         63
  C:006EH         LINE#         64
  C:006EH         LINE#         66
  C:006EH         LINE#         67
  C:0071H         LINE#         68
  -------         ENDPROC       INITINTERRUPTRAM
  -------         PROC          EX0_INT
  C:0006H         LINE#         75
  C:0006H         LINE#         77
  C:0008H         LINE#         78
  C:000AH         LINE#         79
  -------         ENDPROC       EX0_INT
  -------         PROC          TIMER0_INT
  C:0492H         LINE#         86
  C:049AH         LINE#         88
  C:049CH         LINE#         89
  C:049EH         LINE#         90
  C:04A1H         LINE#         91
  C:04A4H         LINE#         92
  C:04A6H         LINE#         98
  C:04ACH         LINE#         100
  C:04B2H         LINE#         101
  C:04BFH         LINE#         102
  C:04BFH         LINE#         103
  C:04C1H         LINE#         105
  C:04C2H         LINE#         106
  C:04C7H         LINE#         107
  C:04C7H         LINE#         108
  C:04C9H         LINE#         109
  C:04C9H         LINE#         111
  C:04CFH         LINE#         112
  C:04D9H         LINE#         113
  C:04D9H         LINE#         114
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 11


  C:04DBH         LINE#         115
  C:04DEH         LINE#         117
  C:04E2H         LINE#         119
  C:04E5H         LINE#         120
  C:04EAH         LINE#         121
  C:04EAH         LINE#         122
  C:04ECH         LINE#         123
  C:04EFH         LINE#         125
  C:04F5H         LINE#         126
  C:04F8H         LINE#         127
  C:04F8H         LINE#         128
  C:04FBH         LINE#         129
  C:04FDH         LINE#         130
  C:04FDH         LINE#         133
  C:050BH         LINE#         134
  C:051AH         LINE#         135
  C:051AH         LINE#         136
  C:051EH         LINE#         137
  C:0521H         LINE#         139
  C:052FH         LINE#         140
  C:053EH         LINE#         141
  C:053EH         LINE#         142
  C:0542H         LINE#         143
  C:0545H         LINE#         144
  C:0545H         LINE#         145
  C:0545H         LINE#         146
  C:0545H         LINE#         171
  C:0545H         LINE#         172
  -------         ENDPROC       TIMER0_INT
  -------         PROC          EX1_INT
  C:000EH         LINE#         179
  C:000EH         LINE#         181
  C:0010H         LINE#         182
  C:0012H         LINE#         183
  -------         ENDPROC       EX1_INT
  -------         PROC          TIMER1_INT
  C:0751H         LINE#         190
  C:0768H         LINE#         192
  C:076AH         LINE#         193
  C:076CH         LINE#         194
  C:076FH         LINE#         195
  C:0772H         LINE#         196
  C:0774H         LINE#         198
  C:077AH         LINE#         199
  C:0781H         LINE#         201
  C:078FH         LINE#         202
  C:07A2H         LINE#         205
  C:07A7H         LINE#         206
  C:07A9H         LINE#         207
  C:07A9H         LINE#         208
  -------         ENDPROC       TIMER1_INT
  -------         PROC          EUART0_INT
  -------         DO            
  X:005DH         SYMBOL        TempSBUF
  -------         ENDDO         
  C:0303H         LINE#         304
  C:0312H         LINE#         308
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 12


  C:0318H         LINE#         309
  C:0318H         LINE#         310
  C:031AH         LINE#         312
  C:0320H         LINE#         313
  C:0326H         LINE#         315
  C:032DH         LINE#         316
  C:032DH         LINE#         317
  C:0336H         LINE#         318
  C:0336H         LINE#         319
  C:0336H         LINE#         320
  C:0344H         LINE#         321
  C:0347H         LINE#         322
  C:034DH         LINE#         324
  C:0350H         LINE#         326
  C:0350H         LINE#         331
  C:0350H         LINE#         332
  C:0350H         LINE#         333
  C:0355H         LINE#         334
  C:0355H         LINE#         335
  C:035EH         LINE#         336
  C:035EH         LINE#         337
  C:035EH         LINE#         338
  C:035EH         LINE#         339
  C:0361H         LINE#         340
  C:0367H         LINE#         342
  C:0369H         LINE#         344
  C:0369H         LINE#         345
  C:036EH         LINE#         346
  C:0372H         LINE#         347
  C:0376H         LINE#         348
  C:0378H         LINE#         349
  C:0378H         LINE#         350
  C:0378H         LINE#         351
  C:0380H         LINE#         352
  C:0380H         LINE#         353
  C:0385H         LINE#         354
  C:0388H         LINE#         356
  C:0393H         LINE#         357
  C:0393H         LINE#         358
  C:0396H         LINE#         359
  C:0396H         LINE#         360
  C:0399H         LINE#         362
  C:039BH         LINE#         363
  C:039BH         LINE#         364
  C:039DH         LINE#         366
  C:039DH         LINE#         367
  C:039DH         LINE#         368
  C:03A0H         LINE#         369
  C:03A2H         LINE#         370
  C:03A2H         LINE#         371
  C:03A2H         LINE#         373
  C:03A5H         LINE#         374
  C:03A5H         LINE#         375
  C:03A7H         LINE#         377
  C:03B2H         LINE#         378
  C:03B2H         LINE#         379
  C:03B5H         LINE#         380
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 13


  C:03C2H         LINE#         381
  C:03C4H         LINE#         383
  C:03C4H         LINE#         384
  C:03C6H         LINE#         385
  C:03C6H         LINE#         386
  C:03C6H         LINE#         387
  -------         ENDPROC       EUART0_INT
  -------         PROC          TIMER2_INT
  C:0016H         LINE#         492
  C:0016H         LINE#         494
  C:0018H         LINE#         495
  C:001AH         LINE#         496
  -------         ENDPROC       TIMER2_INT
  -------         PROC          EX2_INT
  C:0AF8H         LINE#         503
  C:0AF8H         LINE#         505
  C:0AFBH         LINE#         506
  C:0AFEH         LINE#         507
  -------         ENDPROC       EX2_INT
  -------         PROC          SCM_INT
  C:001EH         LINE#         514
  C:001EH         LINE#         516
  C:0021H         LINE#         517
  -------         ENDPROC       SCM_INT
  -------         PROC          PWM_INT
  C:0026H         LINE#         524
  C:0026H         LINE#         526
  C:0029H         LINE#         527
  -------         ENDPROC       PWM_INT
  -------         PROC          ELPD_INT
  C:005EH         LINE#         534
  C:005EH         LINE#         536
  C:0061H         LINE#         537
  -------         ENDPROC       ELPD_INT
  -------         ENDMOD        INTERRUPT

  -------         MODULE        LEDAPP
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:005EH         PUBLIC        LedWorkGroupStr
  C:0B0BH         PUBLIC        InitAllLedRamApp
  C:0665H         PUBLIC        LedApp
  -------         PROC          INITALLLEDRAMAPP
  C:0B0BH         LINE#         50
  C:0B0BH         LINE#         51
  C:0B0BH         LINE#         52
  -------         ENDPROC       INITALLLEDRAMAPP
  -------         PROC          LEDAPP
  C:0665H         LINE#         60
  C:0665H         LINE#         61
  C:0665H         LINE#         62
  C:066DH         LINE#         63
  C:066DH         LINE#         64
  C:066EH         LINE#         66
  C:0686H         LINE#         67
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 14


  C:0686H         LINE#         68
  C:0686H         LINE#         69
  C:0689H         LINE#         70
  C:068CH         LINE#         71
  C:068CH         LINE#         73
  C:068CH         LINE#         74
  C:068FH         LINE#         75
  C:0692H         LINE#         76
  C:0695H         LINE#         77
  C:0695H         LINE#         79
  C:0695H         LINE#         80
  C:0698H         LINE#         81
  C:069BH         LINE#         82
  C:069EH         LINE#         83
  C:06A1H         LINE#         84
  C:06A1H         LINE#         86
  C:06A1H         LINE#         87
  C:06A4H         LINE#         88
  C:06A7H         LINE#         89
  C:06AAH         LINE#         90
  C:06ADH         LINE#         91
  C:06B0H         LINE#         92
  C:06B0H         LINE#         94
  C:06B0H         LINE#         95
  C:06B3H         LINE#         96
  C:06B6H         LINE#         97
  C:06B9H         LINE#         98
  C:06BCH         LINE#         99
  C:06BFH         LINE#         100
  C:06C2H         LINE#         101
  C:06C2H         LINE#         103
  C:06C2H         LINE#         104
  C:06C5H         LINE#         105
  C:06C8H         LINE#         106
  C:06CBH         LINE#         107
  C:06CEH         LINE#         108
  C:06D1H         LINE#         109
  C:06D4H         LINE#         110
  C:06D7H         LINE#         111
  C:06D7H         LINE#         113
  C:06D7H         LINE#         114
  C:06DAH         LINE#         115
  C:06DAH         LINE#         116
  C:06DAH         LINE#         117
  C:06DAH         LINE#         118
  -------         ENDPROC       LEDAPP
  -------         ENDMOD        LEDAPP

  -------         MODULE        KEYAPP
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:05DAH         PUBLIC        KeyProc
  B:0098H.4       PUBLIC        REN
  X:0061H         PUBLIC        g_last_speed_level
  C:0AFFH         PUBLIC        InitKeyProcRam
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 15


  X:0062H         PUBLIC        KeyProcStr
  C:097FH         SYMBOL        Com000F
  C:097FH         SYMBOL        L?0016
  -------         PROC          COM000F
  -------         ENDPROC       COM000F
  -------         PROC          INITKEYPROCRAM
  C:0AFFH         LINE#         40
  C:0AFFH         LINE#         41
  C:0AFFH         LINE#         42
  C:0B04H         LINE#         43
  -------         ENDPROC       INITKEYPROCRAM
  C:0659H         SYMBOL        L?0017
  -------         PROC          KEYPROC
  C:05DAH         LINE#         50
  C:05DAH         LINE#         51
  C:05DAH         LINE#         52
  C:05E2H         LINE#         53
  C:05E2H         LINE#         54
  C:05E3H         LINE#         56
  C:05EBH         LINE#         57
  C:05EBH         LINE#         58
  C:05ECH         LINE#         59
  C:05F4H         LINE#         60
  C:05F4H         LINE#         61
  C:05F5H         LINE#         64
  C:05FBH         LINE#         65
  C:0605H         LINE#         66
  C:0605H         LINE#         67
  C:0608H         LINE#         68
  C:0608H         LINE#         70
  C:0608H         LINE#         72
  C:0608H         LINE#         73
  C:0608H         LINE#         74
  C:0608H         LINE#         75
  C:0608H         LINE#         78
  C:060DH         LINE#         79
  C:060DH         LINE#         80
  C:060DH         LINE#         81
  C:0610H         LINE#         82
  C:0610H         LINE#         83
  C:0610H         LINE#         84
  C:0610H         LINE#         85
  C:0610H         LINE#         87
  C:0618H         LINE#         88
  C:0618H         LINE#         89
  C:0619H         LINE#         91
  C:0621H         LINE#         92
  C:0621H         LINE#         93
  C:0622H         LINE#         94
  C:062AH         LINE#         95
  C:062AH         LINE#         96
  C:062BH         LINE#         98
  C:063BH         LINE#         99
  C:063BH         LINE#         100
  C:063EH         LINE#         101
  C:0640H         LINE#         102
  C:064BH         LINE#         103
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 16


  C:064BH         LINE#         104
  C:0650H         LINE#         105
  C:0650H         LINE#         107
  C:0650H         LINE#         109
  C:0650H         LINE#         110
  C:0650H         LINE#         111
  C:0650H         LINE#         112
  C:0650H         LINE#         115
  C:0655H         LINE#         116
  C:0655H         LINE#         117
  C:0655H         LINE#         118
  C:0658H         LINE#         119
  C:0658H         LINE#         120
  C:0658H         LINE#         121
  C:0658H         LINE#         122
  C:0658H         LINE#         123
  -------         ENDPROC       KEYPROC
  -------         ENDMOD        KEYAPP

  -------         MODULE        COMMUNICATION
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:0000H         PUBLIC        Uart
  C:092AH         PUBLIC        _CalcChecksum
  D:0099H         PUBLIC        SBUF
  C:03D3H         PUBLIC        _TxdDataProc
  C:09C2H         PUBLIC        CommFunProc
  C:06DBH         PUBLIC        CommDealResponse
  B:0098H.4       PUBLIC        REN
  C:0A43H         SYMBOL        Com003C
  C:0A43H         SYMBOL        L?0061
  C:0A46H         SYMBOL        L?0062
  -------         PROC          COM003C
  -------         ENDPROC       COM003C
  -------         PROC          _CALCCHECKSUM
  X:005AH         SYMBOL        CS_Buffer
  D:0005H         SYMBOL        Len
  -------         DO            
  D:0007H         SYMBOL        CS
  D:0006H         SYMBOL        j
  -------         ENDDO         
  C:092AH         LINE#         104
  C:0935H         LINE#         105
  C:0935H         LINE#         106
  C:0937H         LINE#         107
  C:0938H         LINE#         109
  C:093DH         LINE#         110
  C:093DH         LINE#         111
  C:0952H         LINE#         112
  C:0955H         LINE#         113
  C:0955H         LINE#         114
  -------         ENDPROC       _CALCCHECKSUM
  -------         PROC          COMMFUNPROC
  C:09C2H         LINE#         121
  C:09C2H         LINE#         122
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 17


  C:09C2H         LINE#         123
  C:09CAH         LINE#         124
  C:09CAH         LINE#         125
  C:09CBH         LINE#         153
  C:09D0H         LINE#         156
  C:09D8H         LINE#         157
  C:09D8H         LINE#         158
  C:09DBH         LINE#         159
  C:09DBH         LINE#         161
  C:09DBH         LINE#         162
  -------         ENDPROC       COMMFUNPROC
  C:0486H         SYMBOL        L?0063
  -------         PROC          _TXDDATAPROC
  X:0059H         SYMBOL        Ack
  -------         DO            
  D:0001H         SYMBOL        checksum
  -------         ENDDO         
  C:03D3H         LINE#         169
  C:03D8H         LINE#         170
  C:03D8H         LINE#         172
  C:03D8H         LINE#         174
  C:03DDH         LINE#         175
  C:03DDH         LINE#         176
  C:03DFH         LINE#         177
  C:03DFH         LINE#         179
  C:03F4H         LINE#         180
  C:03F4H         LINE#         181
  C:03F4H         LINE#         182
  C:03F4H         LINE#         183
  C:03F4H         LINE#         184
  C:03F4H         LINE#         185
  C:03F4H         LINE#         186
  C:03F6H         LINE#         188
  C:03F6H         LINE#         189
  C:03FAH         LINE#         190
  C:03FCH         LINE#         192
  C:03FCH         LINE#         193
  C:0401H         LINE#         194
  C:0401H         LINE#         195
  C:0401H         LINE#         198
  C:0401H         LINE#         199
  C:0401H         LINE#         207
  C:0403H         LINE#         208
  C:040BH         LINE#         209
  C:040BH         LINE#         210
  C:040DH         LINE#         212
  C:040DH         LINE#         214
  C:0422H         LINE#         215
  C:0422H         LINE#         216
  C:0422H         LINE#         217
  C:0422H         LINE#         218
  C:0422H         LINE#         219
  C:0422H         LINE#         220
  C:0422H         LINE#         221
  C:0424H         LINE#         223
  C:0424H         LINE#         224
  C:0428H         LINE#         225
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 18


  C:042AH         LINE#         227
  C:042AH         LINE#         228
  C:042FH         LINE#         229
  C:042FH         LINE#         230
  C:042FH         LINE#         233
  C:0434H         LINE#         234
  C:0439H         LINE#         242
  C:043BH         LINE#         243
  C:0443H         LINE#         244
  C:0443H         LINE#         245
  C:0448H         LINE#         247
  C:0448H         LINE#         249
  C:045DH         LINE#         250
  C:045DH         LINE#         251
  C:045DH         LINE#         252
  C:045DH         LINE#         253
  C:045DH         LINE#         254
  C:045DH         LINE#         255
  C:045DH         LINE#         256
  C:045FH         LINE#         258
  C:045FH         LINE#         259
  C:0463H         LINE#         260
  C:0465H         LINE#         262
  C:0465H         LINE#         263
  C:046AH         LINE#         264
  C:046AH         LINE#         265
  C:046AH         LINE#         268
  C:046DH         LINE#         269
  C:0472H         LINE#         277
  C:0472H         LINE#         279
  C:0477H         LINE#         280
  C:047DH         LINE#         281
  C:047FH         LINE#         282
  C:0485H         LINE#         283
  -------         ENDPROC       _TXDDATAPROC
  -------         PROC          COMMDEALRESPONSE
  -------         DO            
  D:0007H         SYMBOL        rechecksum
  -------         ENDDO         
  C:06DBH         LINE#         290
  C:06DBH         LINE#         291
  C:06DBH         LINE#         294
  C:06E3H         LINE#         295
  C:06E3H         LINE#         296
  C:06EEH         LINE#         298
  C:06F5H         LINE#         299
  C:06F5H         LINE#         301
  C:070DH         LINE#         302
  C:070DH         LINE#         303
  C:070DH         LINE#         304
  C:0712H         LINE#         305
  C:0712H         LINE#         306
  C:0714H         LINE#         307
  C:0714H         LINE#         308
  C:0719H         LINE#         309
  C:0719H         LINE#         310
  C:071BH         LINE#         311
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 19


  C:071BH         LINE#         312
  C:0720H         LINE#         313
  C:0720H         LINE#         314
  C:0722H         LINE#         315
  C:0722H         LINE#         316
  C:0727H         LINE#         317
  C:0727H         LINE#         318
  C:0729H         LINE#         319
  C:0729H         LINE#         320
  C:072EH         LINE#         321
  C:072EH         LINE#         322
  C:0730H         LINE#         323
  C:0730H         LINE#         324
  C:0736H         LINE#         325
  C:073AH         LINE#         326
  C:073AH         LINE#         327
  C:073AH         LINE#         328
  C:073AH         LINE#         329
  C:073AH         LINE#         332
  C:073CH         LINE#         333
  C:073EH         LINE#         335
  C:073EH         LINE#         337
  C:0743H         LINE#         338
  C:0743H         LINE#         341
  C:0748H         LINE#         342
  C:074CH         LINE#         343
  C:0750H         LINE#         344
  C:0750H         LINE#         345
  -------         ENDPROC       COMMDEALRESPONSE
  -------         ENDMOD        COMMUNICATION

  -------         MODULE        LEDDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00EAH         PUBLIC        P1M0
  D:00E2H         PUBLIC        P1M1
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:0090H.2       PUBLIC        P1_2
  B:0090H.3       PUBLIC        P1_3
  B:0090H.4       PUBLIC        P1_4
  B:0090H.5       PUBLIC        P1_5
  B:0090H.6       PUBLIC        P1_6
  B:0090H.7       PUBLIC        P1_7
  C:0A7FH         PUBLIC        LED1_WorkOff
  C:0A91H         PUBLIC        LED2_WorkOff
  C:0AA3H         PUBLIC        LED3_WorkOff
  C:0AB5H         PUBLIC        LED4_WorkOff
  C:0AC7H         PUBLIC        LED5_WorkOff
  C:0AD9H         PUBLIC        LED6_WorkOff
  C:0A76H         PUBLIC        LED1_WorkOn
  C:0A88H         PUBLIC        LED2_WorkOn
  C:0A9AH         PUBLIC        LED3_WorkOn
  C:0AACH         PUBLIC        LED4_WorkOn
  C:0ABEH         PUBLIC        LED5_WorkOn
  C:0AD0H         PUBLIC        LED6_WorkOn
  C:0B05H         PUBLIC        InitAllLedDriverRam
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 20


  C:0A31H         PUBLIC        InitAllLedOff
  -------         PROC          INITALLLEDDRIVERRAM
  C:0B05H         LINE#         49
  C:0B05H         LINE#         50
  C:0B05H         LINE#         51
  C:0B0AH         LINE#         65
  -------         ENDPROC       INITALLLEDDRIVERRAM
  -------         PROC          INITALLLEDOFF
  C:0A31H         LINE#         72
  C:0A31H         LINE#         73
  C:0A31H         LINE#         74
  C:0A34H         LINE#         75
  C:0A37H         LINE#         76
  C:0A3AH         LINE#         77
  C:0A3DH         LINE#         78
  C:0A40H         LINE#         79
  -------         ENDPROC       INITALLLEDOFF
  -------         PROC          LED1_WORKON
  C:0A76H         LINE#         88
  C:0A76H         LINE#         89
  C:0A76H         LINE#         90
  C:0A7CH         LINE#         91
  C:0A7EH         LINE#         92
  -------         ENDPROC       LED1_WORKON
  -------         PROC          LED1_WORKOFF
  C:0A7FH         LINE#         99
  C:0A7FH         LINE#         100
  C:0A7FH         LINE#         101
  C:0A85H         LINE#         102
  C:0A87H         LINE#         103
  -------         ENDPROC       LED1_WORKOFF
  -------         PROC          LED2_WORKON
  C:0A88H         LINE#         131
  C:0A88H         LINE#         132
  C:0A88H         LINE#         133
  C:0A8EH         LINE#         134
  C:0A90H         LINE#         135
  -------         ENDPROC       LED2_WORKON
  -------         PROC          LED2_WORKOFF
  C:0A91H         LINE#         142
  C:0A91H         LINE#         143
  C:0A91H         LINE#         144
  C:0A97H         LINE#         145
  C:0A99H         LINE#         146
  -------         ENDPROC       LED2_WORKOFF
  -------         PROC          LED3_WORKON
  C:0A9AH         LINE#         175
  C:0A9AH         LINE#         176
  C:0A9AH         LINE#         177
  C:0AA0H         LINE#         178
  C:0AA2H         LINE#         179
  -------         ENDPROC       LED3_WORKON
  -------         PROC          LED3_WORKOFF
  C:0AA3H         LINE#         186
  C:0AA3H         LINE#         187
  C:0AA3H         LINE#         188
  C:0AA9H         LINE#         189
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 21


  C:0AABH         LINE#         190
  -------         ENDPROC       LED3_WORKOFF
  -------         PROC          LED4_WORKON
  C:0AACH         LINE#         218
  C:0AACH         LINE#         219
  C:0AACH         LINE#         220
  C:0AB2H         LINE#         221
  C:0AB4H         LINE#         222
  -------         ENDPROC       LED4_WORKON
  -------         PROC          LED4_WORKOFF
  C:0AB5H         LINE#         229
  C:0AB5H         LINE#         230
  C:0AB5H         LINE#         231
  C:0ABBH         LINE#         232
  C:0ABDH         LINE#         233
  -------         ENDPROC       LED4_WORKOFF
  -------         PROC          LED5_WORKON
  C:0ABEH         LINE#         262
  C:0ABEH         LINE#         263
  C:0ABEH         LINE#         264
  C:0AC4H         LINE#         265
  C:0AC6H         LINE#         266
  -------         ENDPROC       LED5_WORKON
  -------         PROC          LED5_WORKOFF
  C:0AC7H         LINE#         273
  C:0AC7H         LINE#         274
  C:0AC7H         LINE#         275
  C:0ACDH         LINE#         276
  C:0ACFH         LINE#         277
  -------         ENDPROC       LED5_WORKOFF
  -------         PROC          LED6_WORKON
  C:0AD0H         LINE#         305
  C:0AD0H         LINE#         306
  C:0AD0H         LINE#         307
  C:0AD6H         LINE#         308
  C:0AD8H         LINE#         309
  -------         ENDPROC       LED6_WORKON
  -------         PROC          LED6_WORKOFF
  C:0AD9H         LINE#         316
  C:0AD9H         LINE#         317
  C:0AD9H         LINE#         318
  C:0ADFH         LINE#         319
  C:0AE1H         LINE#         320
  -------         ENDPROC       LED6_WORKOFF
  -------         ENDMOD        LEDDRIVER

  -------         MODULE        KEYDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00E4H         PUBLIC        P3M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:004CH         PUBLIC        KeyDrvStr
  B:00B0H.3       PUBLIC        P3_3
  B:00B0H.7       PUBLIC        P3_7
  C:01C6H         PUBLIC        KeyScan
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 22


  C:07BEH         PUBLIC        InitKeyScanRam
  C:0A60H         SYMBOL        Com002D
  C:0A60H         SYMBOL        L?0046
  -------         PROC          COM002D
  -------         ENDPROC       COM002D
  -------         PROC          INITKEYSCANRAM
  -------         DO            
  D:0007H         SYMBOL        i
  -------         ENDDO         
  C:07BEH         LINE#         48
  C:07BEH         LINE#         49
  C:07BEH         LINE#         52
  C:07C3H         LINE#         53
  C:07CEH         LINE#         54
  C:07CEH         LINE#         55
  C:07DAH         LINE#         56
  C:07E6H         LINE#         57
  C:07F2H         LINE#         58
  C:07FEH         LINE#         59
  C:080AH         LINE#         60
  C:0816H         LINE#         61
  C:0819H         LINE#         62
  -------         ENDPROC       INITKEYSCANRAM
  -------         PROC          KEYSCAN
  -------         DO            
  X:0059H         SYMBOL        tempbuff
  -------         ENDDO         
  C:01C6H         LINE#         69
  C:01C6H         LINE#         70
  C:01C6H         LINE#         73
  C:01D1H         LINE#         74
  C:01D1H         LINE#         75
  C:01D2H         LINE#         77
  C:01D8H         LINE#         78
  C:01DEH         LINE#         79
  C:01E0H         LINE#         80
  C:01E2H         LINE#         81
  C:01E3H         LINE#         82
  C:01E4H         LINE#         83
  C:01E5H         LINE#         84
  C:01E6H         LINE#         85
  C:01E7H         LINE#         86
  C:01E8H         LINE#         87
  C:01E9H         LINE#         88
  C:01EAH         LINE#         89
  C:01EBH         LINE#         90
  C:01ECH         LINE#         91
  C:01EFH         LINE#         92
  C:01EFH         LINE#         93
  C:01F4H         LINE#         94
  C:01F6H         LINE#         96
  C:01F6H         LINE#         97
  C:01FBH         LINE#         98
  C:01FBH         LINE#         99
  C:01FEH         LINE#         100
  C:01FEH         LINE#         101
  C:0204H         LINE#         102
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 23


  C:0206H         LINE#         104
  C:0206H         LINE#         105
  C:020BH         LINE#         106
  C:020BH         LINE#         110
  C:0217H         LINE#         111
  C:0217H         LINE#         112
  C:0219H         LINE#         113
  C:021EH         LINE#         114
  C:0220H         LINE#         116
  C:0220H         LINE#         117
  C:0223H         LINE#         118
  C:0228H         LINE#         119
  C:0228H         LINE#         120
  C:022AH         LINE#         122
  C:0237H         LINE#         123
  C:0237H         LINE#         124
  C:023CH         LINE#         126
  C:023FH         LINE#         127
  C:023FH         LINE#         128
  C:0245H         LINE#         129
  C:0247H         LINE#         130
  C:024EH         LINE#         131
  C:024EH         LINE#         132
  C:0251H         LINE#         133
  C:0257H         LINE#         134
  C:025AH         LINE#         135
  C:025CH         LINE#         137
  C:025CH         LINE#         138
  C:025FH         LINE#         139
  C:025FH         LINE#         140
  C:0261H         LINE#         142
  C:0261H         LINE#         143
  C:0265H         LINE#         144
  C:0265H         LINE#         145
  C:026BH         LINE#         146
  C:026DH         LINE#         147
  C:0270H         LINE#         148
  C:0270H         LINE#         149
  C:0276H         LINE#         150
  C:0278H         LINE#         152
  C:0278H         LINE#         153
  C:027DH         LINE#         154
  C:0281H         LINE#         155
  C:0285H         LINE#         156
  C:0289H         LINE#         157
  C:0289H         LINE#         158
  C:0289H         LINE#         159
  C:0289H         LINE#         160
  C:0289H         LINE#         163
  C:0295H         LINE#         164
  C:0295H         LINE#         165
  C:0297H         LINE#         166
  C:029CH         LINE#         167
  C:029DH         LINE#         169
  C:029DH         LINE#         170
  C:02A0H         LINE#         171
  C:02A5H         LINE#         172
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 24


  C:02A5H         LINE#         173
  C:02A7H         LINE#         175
  C:02B4H         LINE#         176
  C:02B4H         LINE#         177
  C:02B9H         LINE#         179
  C:02BCH         LINE#         180
  C:02BCH         LINE#         181
  C:02C2H         LINE#         182
  C:02C3H         LINE#         183
  C:02CAH         LINE#         184
  C:02CAH         LINE#         185
  C:02CDH         LINE#         186
  C:02D3H         LINE#         187
  C:02D6H         LINE#         188
  C:02D8H         LINE#         190
  C:02D8H         LINE#         191
  C:02DBH         LINE#         192
  C:02DBH         LINE#         193
  C:02DCH         LINE#         195
  C:02DCH         LINE#         196
  C:02E0H         LINE#         197
  C:02E0H         LINE#         198
  C:02E6H         LINE#         199
  C:02E7H         LINE#         200
  C:02EAH         LINE#         201
  C:02EAH         LINE#         202
  C:02F0H         LINE#         203
  C:02F1H         LINE#         205
  C:02F1H         LINE#         206
  C:02F6H         LINE#         207
  C:02FAH         LINE#         208
  C:02FEH         LINE#         209
  C:0302H         LINE#         210
  C:0302H         LINE#         211
  C:0302H         LINE#         212
  C:0302H         LINE#         213
  C:0302H         LINE#         214
  C:0302H         LINE#         215
  -------         ENDPROC       KEYSCAN
  -------         ENDMOD        KEYDRIVER

  -------         MODULE        UARTDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00E4H         PUBLIC        P3M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00A8H         PUBLIC        IEN0
  C:08B0H         PUBLIC        _Uart0CalBaudrate
  B:0098H.0       PUBLIC        RI
  D:00CBH         PUBLIC        RCAP2H
  D:00CAH         PUBLIC        RCAP2L
  D:0087H         PUBLIC        PCON
  D:0098H         PUBLIC        SCON
  D:00CDH         PUBLIC        TH2
  D:00CCH         PUBLIC        TL2
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 25


  B:00C8H.2       PUBLIC        TR2
  C:0956H         PUBLIC        Uart0Init
  B:0098H.4       PUBLIC        REN
  D:00C9H         PUBLIC        T2MOD
  D:00C8H         PUBLIC        T2CON
  D:009BH         PUBLIC        SADEN
  D:009AH         PUBLIC        SADDR
  -------         PROC          UART0INIT
  C:0956H         LINE#         49
  C:0956H         LINE#         50
  C:0956H         LINE#         51
  C:0959H         LINE#         52
  C:095CH         LINE#         53
  C:095FH         LINE#         59
  C:0962H         LINE#         60
  C:0965H         LINE#         61
  C:0968H         LINE#         63
  C:096BH         LINE#         64
  C:096DH         LINE#         66
  C:0974H         LINE#         67
  C:0977H         LINE#         68
  C:0979H         LINE#         69
  C:097BH         LINE#         70
  C:097EH         LINE#         71
  -------         ENDPROC       UART0INIT
  -------         PROC          _UART0CALBAUDRATE
  D:0006H         SYMBOL        baudratepar
  -------         DO            
  X:0059H         SYMBOL        CalBaudRateTemp
  -------         ENDDO         
  C:08B0H         LINE#         80
  C:08B0H         LINE#         81
  C:08B0H         LINE#         84
  C:08CEH         LINE#         89
  C:08D1H         LINE#         90
  C:08D4H         LINE#         91
  C:08D7H         LINE#         92
  C:08DAH         LINE#         93
  C:08DDH         LINE#         94
  C:08E0H         LINE#         95
  C:08E3H         LINE#         96
  C:08E5H         LINE#         97
  C:08E7H         LINE#         99
  C:08EAH         LINE#         100
  C:08EDH         LINE#         101
  C:08EFH         LINE#         102
  -------         ENDPROC       _UART0CALBAUDRATE
  -------         ENDMOD        UARTDRIVER

  -------         MODULE        PWMDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  C:0A1EH         PUBLIC        Timer1_Init
  D:0089H         PUBLIC        TMOD
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  17:57:20  PAGE 26


  B:00A8H.3       PUBLIC        ET1
  D:008DH         PUBLIC        TH1
  D:008BH         PUBLIC        TL1
  B:0088H.6       PUBLIC        TR1
  -------         PROC          TIMER1_INIT
  C:0A1EH         LINE#         9
  C:0A1EH         LINE#         10
  C:0A21H         LINE#         11
  C:0A24H         LINE#         12
  C:0A27H         LINE#         13
  C:0A2AH         LINE#         14
  C:0A2CH         LINE#         15
  C:0A2EH         LINE#         16
  C:0A30H         LINE#         17
  -------         ENDPROC       TIMER1_INIT
  -------         ENDMOD        PWMDRIVER

  -------         MODULE        ?C?CLDOPTR
  C:0076H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?UIDIV
  C:00A3H         PUBLIC        ?C?UIDIV
  -------         ENDMOD        ?C?UIDIV

  -------         MODULE        ?C?SLDIV
  C:086CH         PUBLIC        ?C?SLDIV
  -------         ENDMOD        ?C?SLDIV

  -------         MODULE        ?C?ULDIV
  C:0134H         PUBLIC        ?C?ULDIV
  -------         ENDMOD        ?C?ULDIV

Program Size: data=9.0 xdata=99 code=2826
LINK/LOCATE RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
