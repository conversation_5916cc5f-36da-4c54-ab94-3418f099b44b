/*==============================================================================
				Copyright(C) 1997-2019.  Sinowealth Tech. Co., Ltd.
--------------------------------------------------------------------------------
Project Name  : COMMUNICATION.c
Description   :
Notice        :
			 1:
			 2:
Author        : <PERSON>.wang (email: <PERSON>.<EMAIL>)
Start Date    : 2022/07/18
Approve Date  :
Version       : V0.0
Function List :
			 1:
			 2:
RevisionHistory:
Rev#  CheckSum    Date     Author     Comments(Function+Date)
-----+--------+----------+---------+------------------------------------------
0.0     XXXX   2022/07/18 james.wang Just build the file
==============================================================================*/

//-------------------------------------------------------------------------------
#ifndef COMMUNICATION_C
#define COMMUNICATION_C
#endif
//-------------------------------------------------------------------------------

//---------------

#include ".\head\Config.h"

#include <stdlib.h>
#include <STRING.H>
#include <MATH.H>
#include "intrins.h"

void UART_SendByte(unsigned char dat);

/****************************************************************************
 * Function Description:crc16_ccitt_table process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
// code unsigned int crc16_ccitt_table[] =
//	{
//		0x0000, 0x1189, 0x2312, 0x329b, 0x4624, 0x57ad, 0x6536, 0x74bf,
//		0x8c48, 0x9dc1, 0xaf5a, 0xbed3, 0xca6c, 0xdbe5, 0xe97e, 0xf8f7,
//		0x1081, 0x0108, 0x3393, 0x221a, 0x56a5, 0x472c, 0x75b7, 0x643e,
//		0x9cc9, 0x8d40, 0xbfdb, 0xae52, 0xdaed, 0xcb64, 0xf9ff, 0xe876,
//		0x2102, 0x308b, 0x0210, 0x1399, 0x6726, 0x76af, 0x4434, 0x55bd,
//		0xad4a, 0xbcc3, 0x8e58, 0x9fd1, 0xeb6e, 0xfae7, 0xc87c, 0xd9f5,
//		0x3183, 0x200a, 0x1291, 0x0318, 0x77a7, 0x662e, 0x54b5, 0x453c,
//		0xbdcb, 0xac42, 0x9ed9, 0x8f50, 0xfbef, 0xea66, 0xd8fd, 0xc974,
//		0x4204, 0x538d, 0x6116, 0x709f, 0x0420, 0x15a9, 0x2732, 0x36bb,
//		0xce4c, 0xdfc5, 0xed5e, 0xfcd7, 0x8868, 0x99e1, 0xab7a, 0xbaf3,
//		0x5285, 0x430c, 0x7197, 0x601e, 0x14a1, 0x0528, 0x37b3, 0x263a,
//		0xdecd, 0xcf44, 0xfddf, 0xec56, 0x98e9, 0x8960, 0xbbfb, 0xaa72,
//		0x6306, 0x728f, 0x4014, 0x519d, 0x2522, 0x34ab, 0x0630, 0x17b9,
//		0xef4e, 0xfec7, 0xcc5c, 0xddd5, 0xa96a, 0xb8e3, 0x8a78, 0x9bf1,
//		0x7387, 0x620e, 0x5095, 0x411c, 0x35a3, 0x242a, 0x16b1, 0x0738,
//		0xffcf, 0xee46, 0xdcdd, 0xcd54, 0xb9eb, 0xa862, 0x9af9, 0x8b70,
//		0x8408, 0x9581, 0xa71a, 0xb693, 0xc22c, 0xd3a5, 0xe13e, 0xf0b7,
//		0x0840, 0x19c9, 0x2b52, 0x3adb, 0x4e64, 0x5fed, 0x6d76, 0x7cff,
//		0x9489, 0x8500, 0xb79b, 0xa612, 0xd2ad, 0xc324, 0xf1bf, 0xe036,
//		0x18c1, 0x0948, 0x3bd3, 0x2a5a, 0x5ee5, 0x4f6c, 0x7df7, 0x6c7e,
//		0xa50a, 0xb483, 0x8618, 0x9791, 0xe32e, 0xf2a7, 0xc03c, 0xd1b5,
//		0x2942, 0x38cb, 0x0a50, 0x1bd9, 0x6f66, 0x7eef, 0x4c74, 0x5dfd,
//		0xb58b, 0xa402, 0x9699, 0x8710, 0xf3af, 0xe226, 0xd0bd, 0xc134,
//		0x39c3, 0x284a, 0x1ad1, 0x0b58, 0x7fe7, 0x6e6e, 0x5cf5, 0x4d7c,
//		0xc60c, 0xd785, 0xe51e, 0xf497, 0x8028, 0x91a1, 0xa33a, 0xb2b3,
//		0x4a44, 0x5bcd, 0x6956, 0x78df, 0x0c60, 0x1de9, 0x2f72, 0x3efb,
//		0xd68d, 0xc704, 0xf59f, 0xe416, 0x90a9, 0x8120, 0xb3bb, 0xa232,
//		0x5ac5, 0x4b4c, 0x79d7, 0x685e, 0x1ce1, 0x0d68, 0x3ff3, 0x2e7a,
//		0xe70e, 0xf687, 0xc41c, 0xd595, 0xa12a, 0xb0a3, 0x8238, 0x93b1,
//		0x6b46, 0x7acf, 0x4854, 0x59dd, 0x2d62, 0x3ceb, 0x0e70, 0x1ff9,
//		0xf78f, 0xe606, 0xd49d, 0xc514, 0xb1ab, 0xa022, 0x92b9, 0x8330,
//		0x7bc7, 0x6a4e, 0x58d5, 0x495c, 0x3de3, 0x2c6a, 0x1ef1, 0x0f78};

/****************************************************************************
 * Function Description:crc16_x25 process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
// unsigned int crc16_x25(unsigned char *cata, unsigned char length)
//{
//	unsigned char i;
//	unsigned int crc_reg = 0xFFFF;

//	for (i = 0; i < length; i++) // for(i=0;i<8;i++)
//	{
//		crc_reg = (crc_reg >> 8) ^ crc16_ccitt_table[(crc_reg ^ cata[i]) & 0xFF];
//	}
//	return ((~crc_reg) & 0xFFFF);
//}

////////////////////////////////////////////////////////////////////////////////////////////
/*******************************************************************************************
** Function name:       CalcChecksum
** Descriptions:        和校验
** input parameters:    unsigned char  *CS_Buffer : 数据缓冲区首地址
						unsigned short Len：        数据长度
** Returned value:      CS
********************************************************************************************/
unsigned char CalcChecksum(unsigned char *CS_Buffer, unsigned char Len)
{
	unsigned char xdata CS = 0;
	unsigned char xdata j = 0;

	for (j = 0; j < Len; j++)
	{
		CS += CS_Buffer[j];
	}
	return CS;
}

/****************************************************************************
 * Function Description:CommFunProc process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void CommFunProc(void)
{
	if (TimeFlagStr.Task50msFlag == 0x01) // 50ms
	{
		TimeFlagStr.Task50msFlag = 0x00;

		// if (Uart.RxdTimeOutCnt < 100) // 1000*30ms=3S
		// {
		// 	Uart.RxdTimeOutCnt++;
		// }
		// else
		// {
		// 	Uart.RxdTimeOutCnt = 0x00; // 通信异常
		// }

		// if (Uart.RxdFSM == 0x01)
		// {
		// 	if (Uart.RxdTimeCnt < 70) // 10bit,波特率9600,1bit stop,8bit data,无校验，1byte times is 1.04ms
		// 	{
		// 		Uart.RxdTimeCnt++;
		// 	}
		// 	else
		// 	{
		// 		Uart.RxdFSM = 0x00;
		// 		Uart.RxdTimeCnt = 0x00;
		// 	}
		// }
		// else
		// {
		// 	Uart.RxdTimeCnt = 0x00;
		// }
		// 定时发送当前挡位信息
        TxdDataProc(HEAD); // 使用HEAD模式发送主动上报数据
        
        // 如果有接收到的数据需要处理
        if (Uart.RxdResponseFlag == 0x01)
        {
            CommDealResponse(); // 处理接收到的数据
        }
		// CommDealResponse(); // 30ms send once
	}
}

/****************************************************************************
 * Function Description:TxdDataProc process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void TxdDataProc(unsigned char Ack)
{
	//	unsigned int xdata TxCrcCalVal;
	unsigned char checksum = 0x00;

	if (Ack == HEAD)
	{
		Uart.TxdData[0] = HEAD_55; // Recive data CRC verify  abnormal  ---> Feedback NCK(0x15)
		Uart.TxdData[1] = HEAD_AA;

		switch (Uart.Cmd) // Txd data Bit1: feedback Inverter Status
		{
		case STANDBY:
		case STARTUP:
		case OPENLOOP:
		case ERROR:
			Uart.TxdData[2] = 0x00;
			break;

		case RURN:
			Uart.TxdData[2] = KeyProcStr.SpeedLevel;  //发送当前速度等级
			break;

		default:
			Uart.TxdData[2] = 0x00;
			break;
		}

		// 累加校验：对前3个字节求和并取低8位
		checksum = CalcChecksum(&Uart.TxdData[0], 3); //(Uart.TxdData[0] + Uart.TxdData[1] + Uart.TxdData[2]) % 256;
		Uart.TxdData[3] = checksum;
		//		Uart.TxdData[4] = 0x00;
		//		Uart.TxdData[5] = 0x00;
		//		Uart.TxdData[6] = 0x00;
		//		Uart.TxdData[7] = 0x00;
		//		TxCrcCalVal = crc16_x25(Uart.TxdData, 8);
		//		Uart.TxdData[8] = (unsigned char)(TxCrcCalVal >> 8);
		//		Uart.TxdData[9] = (unsigned char)(TxCrcCalVal & 0xFF);
	}
	else if (Ack == ACK) // Txd data Bit0: ACK/NCK
	{
		Uart.TxdData[0] = ACK; // Recive data CRC verify  normal  ---> Feedback ACK(0x06)

		Uart.TxdData[1] = 0xAA;

		switch (Uart.Cmd) // Txd data Bit1: feedback Inverter Status
		{
		case STANDBY:
		case STARTUP:
		case OPENLOOP:
		case ERROR:
			Uart.TxdData[2] = 0x00;
			break;

		case RURN:
			Uart.TxdData[2] = KeyProcStr.SpeedLevel;
			break;

		default:
			Uart.TxdData[2] = 0x00;
			break;
		}

		// 累加校验：对前3个字节求和并取低8位
		checksum = CalcChecksum(&Uart.TxdData[0], 3); //(Uart.TxdData[0] + Uart.TxdData[1] + Uart.TxdData[2]) % 256;
		Uart.TxdData[3] = checksum;
		//		Uart.TxdData[4] = 0x00;
		//		Uart.TxdData[5] = 0x00;
		//		Uart.TxdData[6] = 0x00;
		//		Uart.TxdData[7] = 0x00;
		//		TxCrcCalVal = crc16_x25(Uart.TxdData, 8);
		//		Uart.TxdData[8] = (unsigned char)(TxCrcCalVal >> 8);
		//		Uart.TxdData[9] = (unsigned char)(TxCrcCalVal & 0xFF);
	}
	else if (Ack == NCK)
	{
		Uart.TxdData[0] = NCK; // Recive data CRC verify  abnormal  ---> Feedback NCK(0x15)

		Uart.TxdData[1] = 0xAA;

		switch (Uart.Cmd) // Txd data Bit1: feedback Inverter Status
		{
		case STANDBY:
		case STARTUP:
		case OPENLOOP:
		case ERROR:
			Uart.TxdData[2] = 0x00;
			break;

		case RURN:
			Uart.TxdData[2] = KeyProcStr.SpeedLevel;
			break;

		default:
			Uart.TxdData[2] = 0x00;
			break;
		}

		// 累加校验：对前3个字节求和并取低8位
		checksum = CalcChecksum(&Uart.TxdData[0], 3); //(Uart.TxdData[0] + Uart.TxdData[1] + Uart.TxdData[2]) % 256;
		Uart.TxdData[3] = checksum;
		//		Uart.TxdData[4] = 0x00;
		//		Uart.TxdData[5] = 0x00;
		//		Uart.TxdData[6] = 0x00;
		//		Uart.TxdData[7] = 0x00;
		//		TxCrcCalVal = crc16_x25(Uart.TxdData, 8);
		//		Uart.TxdData[8] = (unsigned char)(TxCrcCalVal >> 8);
		//		Uart.TxdData[9] = (unsigned char)(TxCrcCalVal & 0xFF);
	}

	Uart.TxdDataCnt = 0;
	Uart.TxdDataLength = 3;
	REN = 0;				// 发送使能
	SBUF = Uart.TxdData[0]; // UART数据寄存器
}

/****************************************************************************
 * Function Description:CommDealResponse process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void CommDealResponse(void)
{
	unsigned char rechecksum;

	if (Uart.RxdResponseFlag == 0x01)
	{
		rechecksum = CalcChecksum(&Uart.RxdData[0], 3);

		if (Uart.RxdData[3] == rechecksum)
		{
			// 命令处理
			switch (Uart.RxdData[2])
			{
			case 0x01:
				LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_1;
				KeyProcStr.SpeedLevel = 0x01; // 同时更新挡位变量
				break;
			case 0x02:
				LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_2;
				KeyProcStr.SpeedLevel = 0x02; 
				break;
			case 0x03:
				LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_3;
				KeyProcStr.SpeedLevel = 0x03; 
				break;
			case 0x04:
				LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_4;
				KeyProcStr.SpeedLevel = 0x04; 
				break;
			case 0x05:
				LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_5;
				KeyProcStr.SpeedLevel = 0x05; 
				break;
			case 0x06:
				LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_6;
				KeyProcStr.SpeedLevel = 0x06; 
				break;
			default:
				break;
			}

			// 成功，发送 ACK
			TxdDataProc(ACK);
		}
		else
		{
			// 校验失败，发送 NCK
			TxdDataProc(NCK);
		}

		// 重置标志
		Uart.RxdResponseFlag = 0;
		Uart.RxdCnt = 0;
		Uart.RxdFSM = 0;
	}
}

// void CommDealResponse(void)
//{
//     unsigned char xdata Ack;
//
//     if(Uart.TxdResponseFlag == 0x01)
//     {
//			  Uart.TxdResponseFlag = 0x00;
//				Ack = HEAD;																					//定时发送数据，头码发0x68
//
//				Uart.RxdTimeOutCnt = 0x00;
//				TxdDataProc(Ack);
//		}
//
//
////		if(Uart.RxdResponseFlag == 0x01)
////		{
////				//Uart.Cmd = RURN;
////				//Uart.RxdData[2] = 30;
////				//Uart.RxdData[3] = 10;
////				//Uart.RxdData[4] = 10;
////				//Uart.RxdData[5] = 30;
////				//Uart.RxdData[6] = 10;
////				//Uart.RxdData[7] = 10;
////				//Uart.RxdData[8] = 0;
////				//Uart.RxdData[9] = 0xAA;
////				//Uart.RxdData[10] = 0xBB;

////				Uart.crcRecVal = (Uart.RxdData[9]<<8)|Uart.RxdData[10];								//11byte
////				Uart.crcCalVal = crc16_x25(Uart.RxdData,9);
////				if((Uart.crcRecVal == Uart.crcCalVal)||(Uart.crcRecVal == 0xAABB))   	//测试模式0xAABB
////				{
////						Ack = ACK;											  //接收数据计算CRC同接收到CRC不相等，应答
////						Uart.RxdTimeOutCnt = 0x00;
////				}
////				else
////				{
////						Ack = NCK;												//接收数据计算CRC同接收到CRC不相等，不应答
////						Uart.RxdCnt = 0x00;
////						Uart.RxdFSM = 0x00;
////						Uart.RxdResponseFlag = 0x00;
////				}
////
////				TxdDataProc(Ack);
////				Uart.RxdResponseFlag = 0x00;
////		}
//}
/****************************************************************************
 * Function Description:recived data txd test
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
// void UART_SendByte(unsigned char dat)
// {
// 	unsigned int timeout = 5000; // 设定最大等待次数

// 	SBUF = dat;

// 	while (!TI && timeout--)
// 		;
// 	TI = 0;
// 	REN = 1; // 发送完转接收状态
// }
