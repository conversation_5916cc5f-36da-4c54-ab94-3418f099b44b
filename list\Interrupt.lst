C51 COMPILER V9.59.0.0   INTERRUPT                                                         06/24/2025 18:27:42 PAGE 1   


C51 COMPILER V9.59.0.0, COMPILATION OF MODULE INTERRUPT
OBJECT MODULE PLACED IN .\output\Interrupt.obj
COMPILER INVOKED BY: D:\Keil_v5\C51\BIN\C51.EXE app\Interrupt.c LARGE OPTIMIZE(9,SPEED) BROWSE DEBUG OBJECTEXTEND PRINT(
                    -.\list\Interrupt.lst) TABS(2) OBJECT(.\output\Interrupt.obj)

line level    source

   1          /*==============================================================================
   2                Copyright(C) 2021-2029.  XXXXXX Tech. Co., Ltd.
   3          --------------------------------------------------------------------------------
   4          <USER> <GROUP>.   : XXXXXX
   5          File Name     : INTERRUPT..C
   6          Description   :
   7                  ??
   8                  ??
   9          Notice        :
  10                 1:
  11                 2:
  12          
  13          Author        :
  14          Start Date    :
  15          Release Date  :
  16          Approve Date  :
  17          Version       : V1.0
  18          CheckSum      : XXXXXXX
  19          Function List :
  20                 1:
  21                 2:
  22          
  23          RevisionHistory:
  24          Rev#  CheckSum   Date     Author     Comments(Function+Date)
  25          -----+--------+----------+---------+--------------------------------------------
  26          0.0 2021/10/08 Author Just build the function
  27          0.1
  28          ==============================================================================*/
  29          
  30          //-------------------------------------------------------------------------------
  31          #ifndef INTERRUPT_C
  32          #define INTERRUPT_C
  33          #endif
  34          //-------------------------------------------------------------------------------
  35          
  36          //---------------
  37          
  38          #include ".\head\Config.h"
  39          void UART_SendByte(unsigned char dat);
  40          
  41          // PWM?????????????
  42          unsigned char xdata duty = 0;           // ???PWM??????????
  43          unsigned char xdata count = 0;
  44          unsigned char xdata high_count = 0;
  45          unsigned char xdata duty_shadow = 0;
  46          
  47          /****************************************************************************
  48           * Function Description:InitInterruptRam process
  49           * Input parameter     :void
  50           * Output paramter     :void
  51           ****************************************************************************/
  52          void InitInterruptRam(void)
  53          {
  54   1        TimeFlagStr.Task125usCnt = 0x00;
C51 COMPILER V9.59.0.0   INTERRUPT                                                         06/24/2025 18:27:42 PAGE 2   

  55   1      
  56   1        TimeFlagStr.Task5msCnt = 0x00;
  57   1        TimeFlagStr.Task10msCnt = 0x00;
  58   1        TimeFlagStr.Task10msFlag = 0x00;
  59   1      
  60   1        TimeFlagStr.Task50msCnt = 0x00;
  61   1        TimeFlagStr.Task50msFlag = 0x00;
  62   1      
  63   1        TimeFlagStr.Task500msCnt = 0x00;
  64   1        TimeFlagStr.Task500msFlag = 0x00;
  65   1      
  66   1        TimeFlagStr.Task1000msCnt = 0x00;
  67   1        TimeFlagStr.Task1000msFlag = 0x00;
  68   1      }
  69          
  70          /****************************************************************************
  71           * Function Description:EX0_INT process
  72           * Input parameter     :void
  73           * Output paramter     :void
  74           ****************************************************************************/
  75          void EX0_INT(void) interrupt 0 // ??????0???????:
  76          {
  77   1        IE0 = 0; // TCON.1
  78   1        EX0 = 0; // IE2=EXF0.0,??????0??????????
  79   1      }
  80          
  81          /****************************************************************************
  82           * Function Description:Timer0_INT process
  83           * Input parameter     :void
  84           * Output paramter     :void
  85           ****************************************************************************/
  86          void Timer0_INT(void) interrupt 1 ////???????0???????,125US test is ok
  87          {
  88   1        TF0 = 0; // TCON.5??????????????
  89   1        TR0 = 0;
  90   1        TL0 = (unsigned char)((D_T0_125US_CNT >> 0) & 0x00FF); // t0??????
  91   1        TH0 = (unsigned char)((D_T0_125US_CNT >> 8) & 0x00FF); // t0???????;D_T0_125US_CNT
  92   1        TR0 = 1;
  93   1      
  94   1        ////test
  95   1        // P1_5 = ~P1_5;
  96   1        ////test
  97   1      
  98   1        TimeFlagStr.TaskMonitorFlag = 0x01; // ????125us??????????????
  99   1      
 100   1        TimeFlagStr.Task125usCnt++;
 101   1        if (TimeFlagStr.Task125usCnt >= 8) // 125us*8=1ms,
 102   1        {
 103   2          TimeFlagStr.Task125usCnt = 0x00;
 104   2      
 105   2          TimeFlagStr.Task5msCnt++;
 106   2          if (TimeFlagStr.Task5msCnt >= 5) // 5ms
 107   2          {
 108   3            TimeFlagStr.Task5msCnt = 0x00;
 109   3          }
 110   2      
 111   2          TimeFlagStr.Task10msCnt++;
 112   2          if (TimeFlagStr.Task10msCnt >= 10) // 10ms
 113   2          {
 114   3            TimeFlagStr.Task10msCnt = 0x00;
 115   3            TimeFlagStr.Task10msFlag = 0x01;
 116   3      
C51 COMPILER V9.59.0.0   INTERRUPT                                                         06/24/2025 18:27:42 PAGE 3   

 117   3            KeyDrvStr.KeyScanFlag = 0x01;
 118   3      
 119   3            TimeFlagStr.Task50msCnt++;
 120   3            if (TimeFlagStr.Task50msCnt >= 5) // 10ms*3=30ms
 121   3            {
 122   4              TimeFlagStr.Task50msCnt = 0x00;
 123   4              TimeFlagStr.Task50msFlag = 0x01;
 124   4      
 125   4              Uart.Cmd = RURN;
 126   4              Uart.TxdResponseFlag = 0x01; // ???????
 127   4              Uart.RxdCnt = 0x00;
 128   4              Uart.RxdFSM = 0x00;
 129   4              REN = 1; // ???????
 130   4            }
 131   3      
 132   3            // ???????
 133   3            TimeFlagStr.Task500msCnt++; // every 10ms interrupt,0.5s need 50 times
 134   3            if (TimeFlagStr.Task500msCnt >= 50)
 135   3            {
 136   4              TimeFlagStr.Task500msCnt = 0x00;
 137   4              TimeFlagStr.Task500msFlag = 0x01;
 138   4      
 139   4              TimeFlagStr.Task1000msCnt++; // every 500ms interrupt,1s need 2 times
 140   4              if (TimeFlagStr.Task1000msCnt >= 2)
 141   4              {
 142   5                TimeFlagStr.Task1000msCnt = 0x00;
 143   5                TimeFlagStr.Task1000msFlag = 0x01;
 144   5              }
 145   4            }
 146   3          }
 147   2      
 148   2          //        if(LedWorkGroupStr.Led1FlashMode == 0x01)
 149   2          //        {
 150   2          //            LedWorkGroupStr.Led1SlowFlashCnt = 0x00;
 151   2          //            LedWorkGroupStr.Led1FastFlashCnt++;
 152   2          //            if(LedWorkGroupStr.Led1FastFlashCnt >= 16)            //20
 153   2          //            {
 154   2          //                LedWorkGroupStr.Led1FastFlashCnt = 0x00;
 155   2          //            }
 156   2          //        }
 157   2          //        else if(LedWorkGroupStr.Led1FlashMode == 0x02)
 158   2          //        {
 159   2          //            LedWorkGroupStr.Led1FastFlashCnt = 0x00;
 160   2          //            LedWorkGroupStr.Led1SlowFlashCnt++;
 161   2          //            if(LedWorkGroupStr.Led1SlowFlashCnt >= 48)            //50
 162   2          //            {
 163   2          //                LedWorkGroupStr.Led1SlowFlashCnt = 0x00;
 164   2          //            }
 165   2          //        }
 166   2          //        else
 167   2          //        {
 168   2          //            LedWorkGroupStr.Led1SlowFlashCnt = 0x00;
 169   2          //            LedWorkGroupStr.Led1FastFlashCnt = 0x00;
 170   2          //        }
 171   2        }
 172   1      }
 173          
 174          /****************************************************************************
 175           * Function Description:EX1_INT process
 176           * Input parameter     :void
 177           * Output paramter     :void
 178           ****************************************************************************/
C51 COMPILER V9.59.0.0   INTERRUPT                                                         06/24/2025 18:27:42 PAGE 4   

 179          void EX1_INT(void) interrupt 2 // ??????1???????
 180          {
 181   1        IE1 = 0; // TCON.1
 182   1        EX1 = 0; // IE2=EXF0.0,??????2??????????
 183   1      }
 184          
 185          /****************************************************************************
 186           * Function Description:Timer1_INT process
 187           * Input parameter     :void
 188           * Output paramter     :void
 189           ****************************************************************************/
 190          void Timer1_INT(void) interrupt 3 // ???????1???????:
 191          {
 192   1          TF1 = 0;  // ????????
 193   1      
 194   1          // ?????????????,?100us????
 195   1          TL1 = 0xA0;
 196   1          TH1 = 0xFF;
 197   1      
 198   1          count++;
 199   1          if (PWM_IN) high_count++;
 200   1      
 201   1          if (count >= 100) {  // ?????????,?10ms
 202   2              // ??????,???????
 203   2              if (count > 0) {
 204   3                  duty = (high_count * 100) / count;
 205   3              }
 206   2      
 207   2              // ???????
 208   2              count = 0;
 209   2              high_count = 0;
 210   2          }
 211   1      }
 212          
 213          
 214          ///****************************************************************************
 215          // * Function Description:EUART0_INT process
 216          // * Input parameter     :void
 217          // * Output paramter     :void
 218          // ****************************************************************************/
 219          // void EUART0_INT(void) interrupt 4
 220          //{
 221          //    unsigned char xdata TempSBUF;
 222          
 223          //    // ========== ???????? ==========
 224          //    if (RI)
 225          //    {
 226          //        LED1 ^= 1;
 227          //        RI = 0;
 228          //        TempSBUF = SBUF;
 229          //        UART_SendByte(TempSBUF);
 230          //        switch (Uart.RxdFSM)
 231          //        {
 232          //            case 0X00: // ?????1 (0x55)
 233          //                if (TempSBUF == 0x55)
 234          //                {
 235          //                    Uart.RxdData[0] = TempSBUF;
 236          //                    Uart.RxdCnt = 1;
 237          //                    Uart.RxdFSM = 1;
 238          //                    UART_SendByte(TempSBUF);
 239          //                }
 240          //                else
C51 COMPILER V9.59.0.0   INTERRUPT                                                         06/24/2025 18:27:42 PAGE 5   

 241          //                {
 242          //                    Uart.RxdCnt = 0;
 243          //                    Uart.RxdFSM = 0;
 244          //                }
 245          //                UART_SendByte(TempSBUF); // ??????????
 246          //                break;
 247          
 248          //            case 0X01: // ?????2 (0xAA)
 249          //                if (TempSBUF == 0xAA)
 250          //                {
 251          //                    Uart.RxdData[1] = TempSBUF;
 252          //                    Uart.RxdCnt = 2;
 253          //                    Uart.RxdFSM = 2;
 254          //                }
 255          //                else
 256          //                {
 257          //                    Uart.RxdCnt = 0;
 258          //                    Uart.RxdFSM = 0;
 259          //                }
 260          //                UART_SendByte(TempSBUF);
 261          //                break;
 262          
 263          //            case 0X02: // ??????????
 264          //                Uart.RxdData[2] = TempSBUF;
 265          //                Uart.RxdCnt = 3;
 266          //                Uart.RxdFSM = 3;
 267          //                UART_SendByte(TempSBUF);
 268          //                break;
 269          
 270          //            case 0X03: // ??????????
 271          //                Uart.RxdData[3] = TempSBUF;
 272          //                Uart.RxdCnt = 4;
 273          //                Uart.RxdFSM = 0;
 274          //                Uart.RxdResponseFlag = 0x01;
 275          //                CommDealResponse(); //???????????????????
 276          //                UART_SendByte(TempSBUF);
 277          //                UART_SendByte(Uart.RxdResponseFlag);
 278          //                break;
 279          
 280          //            default:
 281          //                Uart.RxdCnt = 0;
 282          //                Uart.RxdFSM = 0;
 283          //                break;
 284          //        }
 285          //    }
 286          
 287          //
 288          //  // ========== ???????? ==========
 289          //    if (TI)
 290          //    {
 291          //        LED3 ^= 1;
 292          //        TI = 0;
 293          
 294          //        if (Uart.TxdDataCnt < Uart.TxdDataLength)
 295          //        {
 296          //            Uart.TxdDataCnt++;
 297          //            SBUF = Uart.TxdData[Uart.TxdDataCnt];
 298          //        }
 299          //        else
 300          //        {
 301          //            REN = 1; // ???????
 302          //        }
C51 COMPILER V9.59.0.0   INTERRUPT                                                         06/24/2025 18:27:42 PAGE 6   

 303          //    }
 304          //
 305          //}
 306          
 307          void EUART0_INT(void) interrupt 4 // EUART??????????
 308          {
 309   1        unsigned char xdata TempSBUF;
 310   1      
 311   1        if (RI)
 312   1        {
 313   2          RI = 0;
 314   2          
 315   2          TempSBUF = SBUF;    // ????????????
 316   2          Uart.RxdDataLength = 4; // Master data package : Fixed 4 Byte
 317   2          
 318   2          if (Uart.RxdFSM == 0x00)
 319   2          {
 320   3            switch (TempSBUF)
 321   3            {
 322   4            case HEAD_55: // ????(?)0x55
 323   4              Uart.RxdData[Uart.RxdCnt] = TempSBUF;
 324   4              Uart.RxdCnt++;
 325   4              Uart.RxdFSM = 0x01;
 326   4              
 327   4              break;
 328   4      
 329   4            default:
 330   4              Uart.Cmd = 0x00;
 331   4              Uart.RxdCnt = 0x0;
 332   4              Uart.RxdFSM = 0x00;
 333   4              break;
 334   4            }
 335   3          }
 336   2          else if (Uart.RxdFSM == 0x01)
 337   2          {
 338   3            switch (TempSBUF)
 339   3            {
 340   4            case HEAD_AA: // ????(?)0xAA
 341   4              Uart.RxdData[Uart.RxdCnt] = TempSBUF;
 342   4              Uart.RxdCnt++;
 343   4              Uart.RxdFSM = 0x02;
 344   4              
 345   4              break;
 346   4      
 347   4            default:
 348   4              Uart.Cmd = 0x00;
 349   4              Uart.RxdCnt = 0x0;
 350   4              Uart.RxdFSM = 0x00;
 351   4              break;
 352   4            }
 353   3          }
 354   2          else if (Uart.RxdFSM == 0x02)
 355   2          {
 356   3            Uart.RxdData[Uart.RxdCnt] = TempSBUF;
 357   3            Uart.RxdCnt++;
 358   3      
 359   3            if (Uart.RxdCnt >= Uart.RxdDataLength)
 360   3            {
 361   4              Uart.RxdResponseFlag = 0x01; // ????10?????ok!!!
 362   4              Uart.RxdCnt = 0x00;
 363   4              Uart.RxdFSM = 0x00;
 364   4              
C51 COMPILER V9.59.0.0   INTERRUPT                                                         06/24/2025 18:27:42 PAGE 7   

 365   4              REN = 0; // ???????
 366   4            }
 367   3          }
 368   2          else
 369   2          {
 370   3            Uart.RxdCnt = 0x00;
 371   3            Uart.RxdFSM = 0x00;
 372   3            REN = 1; // ???????
 373   3          }
 374   2        }
 375   1      
 376   1        if (TI)
 377   1        {
 378   2          TI = 0;
 379   2      
 380   2          if (Uart.TxdDataCnt < Uart.TxdDataLength)
 381   2          {
 382   3            Uart.TxdDataCnt++;
 383   3            SBUF = Uart.TxdData[Uart.TxdDataCnt]; // ????????????
 384   3          }
 385   2          else
 386   2          {
 387   3            REN = 1; // ???????
 388   3          }
 389   2        }
 390   1      }
 391          
 392          // void UART_SendByte(unsigned char dat) //??????????
 393          //{
 394          //     while (!TI);  // ???????????????
 395          //     TI = 0;
 396          //     SBUF = dat;
 397          // }
 398          
 399          // void EUART0_INT(void) interrupt 4                    //EUART??????????
 400          //{
 401          //    unsigned char data TempSBUF;
 402          
 403          //    if(RI)
 404          //    {
 405          //        RI = 0;
 406          //
 407          //        TempSBUF  = SBUF;                           //????????????
 408          //        Uart.RxdDataLength = 11;                    //Master data package : Fixed 11 Byte
 409          //
 410          //        if(Uart.RxdFSM == 0)
 411          //        {
 412          //            switch(TempSBUF)
 413          //            {
 414          //                case HEAD:                          //????(?)0x55
 415          //                    Uart.RxdData[Uart.RxdCnt] = TempSBUF;
 416          //                    Uart.RxdCnt++;
 417          //                    Uart.RxdFSM = 0x01;
 418          //                break;
 419          //
 420          //                default:
 421          //                    Uart.Cmd   = 0x00;
 422          //                    Uart.RxdCnt = 0x0;
 423          //                    Uart.RxdFSM = 0x00;
 424          //                break;
 425          //            }
 426          //        }
C51 COMPILER V9.59.0.0   INTERRUPT                                                         06/24/2025 18:27:42 PAGE 8   

 427          //        else if(Uart.RxdFSM == 0x01)
 428          //        {
 429          //            switch(TempSBUF)
 430          //            {
 431          //                case CHOICE:                        //0x10       /* ??? */
 432          //                case STANDBY:                       //0x11       /* ???? */
 433          //                case STARTUP:                       //0x12       /* ????? */
 434          //                case OPENLOOP:                      //0x14       /* ??????????? */
 435          //                case RURN:                          //0xAA       /* ????????1 */
 436          //                case ERROR:                         //0x24       /* ERROR??? */
 437          //                case ERRRESET:                      //0xFE       /* ?????? */
 438          //                    Uart.Cmd = TempSBUF;
 439          //                    Uart.RxdData[Uart.RxdCnt] = TempSBUF;
 440          //                    Uart.RxdCnt++;
 441          //                    Uart.RxdFSM = 0x02;
 442          //                break;
 443          //
 444          //                default:
 445          //                    Uart.Cmd = 0x00;
 446          //                    Uart.RxdCnt = 0x00;
 447          //                    Uart.RxdFSM = 0x00;
 448          //                break;
 449          //            }
 450          //        }
 451          //        else if(Uart.RxdFSM == 0x02)
 452          //        {
 453          //            Uart.RxdData[Uart.RxdCnt] = TempSBUF;
 454          //            Uart.RxdCnt++;
 455          //
 456          //            if(Uart.RxdCnt >= Uart.RxdDataLength)
 457          //            {
 458          //                Uart.RxdResponseFlag = 0x01;      //????10?????ok!!!
 459          //                Uart.RxdCnt = 0x00;
 460          //                Uart.RxdFSM = 0x00;
 461          //                REN = 0;                          //???????
 462          //            }
 463          //        }
 464          //        else
 465          //        {
 466          //            Uart.RxdCnt = 0x00;
 467          //            Uart.RxdFSM = 0x00;
 468          //            REN = 1;                              //???????
 469          //        }
 470          //    }
 471          
 472          //
 473          //    if(TI)
 474          //    {
 475          //        TI = 0;
 476          //
 477          //        if(Uart.TxdDataCnt < Uart.TxdDataLength)
 478          //        {
 479          //            Uart.TxdDataCnt++;
 480          //            SBUF = Uart.TxdData[Uart.TxdDataCnt];  //????????????
 481          //        }
 482          //        else
 483          //        {
 484          //            REN = 1;                                //???????
 485          //        }
 486          //    }
 487          //
 488          //}
C51 COMPILER V9.59.0.0   INTERRUPT                                                         06/24/2025 18:27:42 PAGE 9   

 489          
 490          /****************************************************************************
 491           * Function Description:Timer2_INT process
 492           * Input parameter     :void
 493           * Output paramter     :void
 494           ****************************************************************************/
 495          void Timer2_INT(void) interrupt 5 // ???????2???????
 496          {
 497   1        EXF2 = 0;
 498   1        TF2 = 0; // EXF2=T2CON.6 TF2=T2CON.7
 499   1      }
 500          
 501          /****************************************************************************
 502           * Function Description:EX2_INT process
 503           * Input parameter     :void
 504           * Output paramter     :void
 505           ****************************************************************************/
 506          void EX2_INT(void) interrupt 9 // ??????2???????
 507          {
 508   1        EXF0 = Bin(00000100);  // IE2=EXF0.0,????????2????????????BIT3??BIT2??=01,????????
 509   1        IEN1 &= Bin(11111011); // BIT2,EX2 = 0,?????????2
 510   1      }
 511          
 512          /****************************************************************************
 513           * Function Description:SCM_INT process
 514           * Input parameter     :void
 515           * Output paramter     :void
 516           ****************************************************************************/
 517          void SCM_INT(void) interrupt 11
 518          {
 519   1        CLKCON &= Bin(11101111); // SCMIF??????????????????
 520   1      }
 521          
 522          /****************************************************************************
 523           * Function Description:PWM_INT process
 524           * Input parameter     :void
 525           * Output paramter     :void
 526           ****************************************************************************/
 527          void PWM_INT(void) interrupt 12 // PWM??????????
 528          {
 529   1        PWMCON &= Bin(11111101); // PWMIF=0;
 530   1      }
 531          
 532          /****************************************************************************
 533           * Function Description:ELPD_INT process
 534           * Input parameter     :void
 535           * Output paramter     :void
 536           ****************************************************************************/
 537          void ELPD_INT(void) interrupt 14 // LPD??????????
 538          {
 539   1        LPDCON &= Bin(10111111); // LPDF=0;
 540   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    620    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =     17       1
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
C51 COMPILER V9.59.0.0   INTERRUPT                                                         06/24/2025 18:27:42 PAGE 10  

END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
