C51 COMPILER V9.59.0.0   COMMUNICATION                                                     06/24/2025 18:10:58 PAGE 1   


C51 COMPILER V9.59.0.0, COMPILATION OF MODULE COMMUNICATION
OBJECT MODULE PLACED IN .\output\Communication.obj
COMPILER INVOKED BY: D:\Keil_v5\C51\BIN\C51.EXE app\Communication.c LARGE OPTIMIZE(9,SPEED) BROWSE DEBUG OBJECTEXTEND PR
                    -INT(.\list\Communication.lst) TABS(2) OBJECT(.\output\Communication.obj)

line level    source

   1          /*==============================================================================
   2                  Copyright(C) 1997-2019.  Sinowealth Tech. Co., Ltd.
   3          --------------------------------------------------------------------------------
   4          <USER> <GROUP>  : COMMUNICATION.c
   5          Description   :
   6          Notice        :
   7                 1:
   8                 2:
   9          Author        : James.wang (email: <EMAIL>)
  10          Start Date    : 2022/07/18
  11          Approve Date  :
  12          Version       : V0.0
  13          Function List :
  14                 1:
  15                 2:
  16          RevisionHistory:
  17          Rev#  CheckSum    Date     Author     Comments(Function+Date)
  18          -----+--------+----------+---------+------------------------------------------
  19          0.0     XXXX   2022/07/18 james.wang Just build the file
  20          ==============================================================================*/
  21          
  22          //-------------------------------------------------------------------------------
  23          #ifndef COMMUNICATION_C
  24          #define COMMUNICATION_C
  25          #endif
  26          //-------------------------------------------------------------------------------
  27          
  28          //---------------
  29          
  30          #include ".\head\Config.h"
  31          
  32          #include <stdlib.h>
  33          #include <STRING.H>
  34          #include <MATH.H>
  35          #include "intrins.h"
  36          
  37          void UART_SendByte(unsigned char dat);
  38          
  39          /****************************************************************************
  40           * Function Description:crc16_ccitt_table process
  41           * Input parameter     :void
  42           * Output paramter     :void
  43           ****************************************************************************/
  44          // code unsigned int crc16_ccitt_table[] =
  45          //  {
  46          //    0x0000, 0x1189, 0x2312, 0x329b, 0x4624, 0x57ad, 0x6536, 0x74bf,
  47          //    0x8c48, 0x9dc1, 0xaf5a, 0xbed3, 0xca6c, 0xdbe5, 0xe97e, 0xf8f7,
  48          //    0x1081, 0x0108, 0x3393, 0x221a, 0x56a5, 0x472c, 0x75b7, 0x643e,
  49          //    0x9cc9, 0x8d40, 0xbfdb, 0xae52, 0xdaed, 0xcb64, 0xf9ff, 0xe876,
  50          //    0x2102, 0x308b, 0x0210, 0x1399, 0x6726, 0x76af, 0x4434, 0x55bd,
  51          //    0xad4a, 0xbcc3, 0x8e58, 0x9fd1, 0xeb6e, 0xfae7, 0xc87c, 0xd9f5,
  52          //    0x3183, 0x200a, 0x1291, 0x0318, 0x77a7, 0x662e, 0x54b5, 0x453c,
  53          //    0xbdcb, 0xac42, 0x9ed9, 0x8f50, 0xfbef, 0xea66, 0xd8fd, 0xc974,
  54          //    0x4204, 0x538d, 0x6116, 0x709f, 0x0420, 0x15a9, 0x2732, 0x36bb,
C51 COMPILER V9.59.0.0   COMMUNICATION                                                     06/24/2025 18:10:58 PAGE 2   

  55          //    0xce4c, 0xdfc5, 0xed5e, 0xfcd7, 0x8868, 0x99e1, 0xab7a, 0xbaf3,
  56          //    0x5285, 0x430c, 0x7197, 0x601e, 0x14a1, 0x0528, 0x37b3, 0x263a,
  57          //    0xdecd, 0xcf44, 0xfddf, 0xec56, 0x98e9, 0x8960, 0xbbfb, 0xaa72,
  58          //    0x6306, 0x728f, 0x4014, 0x519d, 0x2522, 0x34ab, 0x0630, 0x17b9,
  59          //    0xef4e, 0xfec7, 0xcc5c, 0xddd5, 0xa96a, 0xb8e3, 0x8a78, 0x9bf1,
  60          //    0x7387, 0x620e, 0x5095, 0x411c, 0x35a3, 0x242a, 0x16b1, 0x0738,
  61          //    0xffcf, 0xee46, 0xdcdd, 0xcd54, 0xb9eb, 0xa862, 0x9af9, 0x8b70,
  62          //    0x8408, 0x9581, 0xa71a, 0xb693, 0xc22c, 0xd3a5, 0xe13e, 0xf0b7,
  63          //    0x0840, 0x19c9, 0x2b52, 0x3adb, 0x4e64, 0x5fed, 0x6d76, 0x7cff,
  64          //    0x9489, 0x8500, 0xb79b, 0xa612, 0xd2ad, 0xc324, 0xf1bf, 0xe036,
  65          //    0x18c1, 0x0948, 0x3bd3, 0x2a5a, 0x5ee5, 0x4f6c, 0x7df7, 0x6c7e,
  66          //    0xa50a, 0xb483, 0x8618, 0x9791, 0xe32e, 0xf2a7, 0xc03c, 0xd1b5,
  67          //    0x2942, 0x38cb, 0x0a50, 0x1bd9, 0x6f66, 0x7eef, 0x4c74, 0x5dfd,
  68          //    0xb58b, 0xa402, 0x9699, 0x8710, 0xf3af, 0xe226, 0xd0bd, 0xc134,
  69          //    0x39c3, 0x284a, 0x1ad1, 0x0b58, 0x7fe7, 0x6e6e, 0x5cf5, 0x4d7c,
  70          //    0xc60c, 0xd785, 0xe51e, 0xf497, 0x8028, 0x91a1, 0xa33a, 0xb2b3,
  71          //    0x4a44, 0x5bcd, 0x6956, 0x78df, 0x0c60, 0x1de9, 0x2f72, 0x3efb,
  72          //    0xd68d, 0xc704, 0xf59f, 0xe416, 0x90a9, 0x8120, 0xb3bb, 0xa232,
  73          //    0x5ac5, 0x4b4c, 0x79d7, 0x685e, 0x1ce1, 0x0d68, 0x3ff3, 0x2e7a,
  74          //    0xe70e, 0xf687, 0xc41c, 0xd595, 0xa12a, 0xb0a3, 0x8238, 0x93b1,
  75          //    0x6b46, 0x7acf, 0x4854, 0x59dd, 0x2d62, 0x3ceb, 0x0e70, 0x1ff9,
  76          //    0xf78f, 0xe606, 0xd49d, 0xc514, 0xb1ab, 0xa022, 0x92b9, 0x8330,
  77          //    0x7bc7, 0x6a4e, 0x58d5, 0x495c, 0x3de3, 0x2c6a, 0x1ef1, 0x0f78};
  78          
  79          /****************************************************************************
  80           * Function Description:crc16_x25 process
  81           * Input parameter     :void
  82           * Output paramter     :void
  83           ****************************************************************************/
  84          // unsigned int crc16_x25(unsigned char *cata, unsigned char length)
  85          //{
  86          //  unsigned char i;
  87          //  unsigned int crc_reg = 0xFFFF;
  88          
  89          //  for (i = 0; i < length; i++) // for(i=0;i<8;i++)
  90          //  {
  91          //    crc_reg = (crc_reg >> 8) ^ crc16_ccitt_table[(crc_reg ^ cata[i]) & 0xFF];
  92          //  }
  93          //  return ((~crc_reg) & 0xFFFF);
  94          //}
  95          
  96          ////////////////////////////////////////////////////////////////////////////////////////////
  97          /*******************************************************************************************
  98          ** Function name:       CalcChecksum
  99          ** Descriptions:        和校验
 100          ** input parameters:    unsigned char  *CS_Buffer : 数据缓冲区首地址
 101                      unsigned short Len：        数据长度
 102          ** Returned value:      CS
 103          ********************************************************************************************/
 104          unsigned char CalcChecksum(unsigned char *CS_Buffer, unsigned char Len)
 105          {
 106   1        unsigned char xdata CS = 0;
 107   1        unsigned char xdata j = 0;
 108   1      
 109   1        for (j = 0; j < Len; j++)
 110   1        {
 111   2          CS += CS_Buffer[j];
 112   2        }
 113   1        return CS;
 114   1      }
 115          
 116          /****************************************************************************
C51 COMPILER V9.59.0.0   COMMUNICATION                                                     06/24/2025 18:10:58 PAGE 3   

 117           * Function Description:CommFunProc process
 118           * Input parameter     :void
 119           * Output paramter     :void
 120           ****************************************************************************/
 121          void CommFunProc(void)
 122          {
 123   1        if (TimeFlagStr.Task50msFlag == 0x01) // 50ms
 124   1        {
 125   2          TimeFlagStr.Task50msFlag = 0x00;
 126   2      
 127   2          // if (Uart.RxdTimeOutCnt < 100) // 1000*30ms=3S
 128   2          // {
 129   2          //  Uart.RxdTimeOutCnt++;
 130   2          // }
 131   2          // else
 132   2          // {
 133   2          //  Uart.RxdTimeOutCnt = 0x00; // 通信异常
 134   2          // }
 135   2      
 136   2          // if (Uart.RxdFSM == 0x01)
 137   2          // {
 138   2          //  if (Uart.RxdTimeCnt < 70) // 10bit,波特率9600,1bit stop,8bit data,无校验，1byte times is 1.04
             -ms
 139   2          //  {
 140   2          //    Uart.RxdTimeCnt++;
 141   2          //  }
 142   2          //  else
 143   2          //  {
 144   2          //    Uart.RxdFSM = 0x00;
 145   2          //    Uart.RxdTimeCnt = 0x00;
 146   2          //  }
 147   2          // }
 148   2          // else
 149   2          // {
 150   2          //  Uart.RxdTimeCnt = 0x00;
 151   2          // }
 152   2          // 定时发送当前挡位信息
 153   2              TxdDataProc(HEAD); // 使用HEAD模式发送主动上报数据
 154   2              
 155   2              // 如果有接收到的数据需要处理
 156   2              if (Uart.RxdResponseFlag == 0x01)
 157   2              {
 158   3                  CommDealResponse(); // 处理接收到的数据
 159   3              }
 160   2          // CommDealResponse(); // 30ms send once
 161   2        }
 162   1      }
 163          
 164          /****************************************************************************
 165           * Function Description:TxdDataProc process
 166           * Input parameter     :void
 167           * Output paramter     :void
 168           ****************************************************************************/
 169          void TxdDataProc(unsigned char Ack)
 170          {
 171   1        //  unsigned int xdata TxCrcCalVal;
 172   1        unsigned char checksum = 0x00;
 173   1      
 174   1        if (Ack == HEAD)
 175   1        {
 176   2          Uart.TxdData[0] = HEAD_55; // Recive data CRC verify  abnormal  ---> Feedback NCK(0x15)
 177   2          Uart.TxdData[1] = HEAD_AA;
C51 COMPILER V9.59.0.0   COMMUNICATION                                                     06/24/2025 18:10:58 PAGE 4   

 178   2      
 179   2          switch (Uart.Cmd) // Txd data Bit1: feedback Inverter Status
 180   2          {
 181   3          case STANDBY:
 182   3          case STARTUP:
 183   3          case OPENLOOP:
 184   3          case ERROR:
 185   3            Uart.TxdData[2] = 0x00;
 186   3            break;
 187   3      
 188   3          case RURN:
 189   3            Uart.TxdData[2] = KeyProcStr.SpeedLevel;  //发送当前速度等级
 190   3            break;
 191   3      
 192   3          default:
 193   3            Uart.TxdData[2] = 0x00;
 194   3            break;
 195   3          }
 196   2      
 197   2          // 累加校验：对前3个字节求和并取低8位
 198   2          checksum = CalcChecksum(&Uart.TxdData[0], 3); //(Uart.TxdData[0] + Uart.TxdData[1] + Uart.TxdData[2]) % 
             -256;
 199   2          Uart.TxdData[3] = checksum;
 200   2          //    Uart.TxdData[4] = 0x00;
 201   2          //    Uart.TxdData[5] = 0x00;
 202   2          //    Uart.TxdData[6] = 0x00;
 203   2          //    Uart.TxdData[7] = 0x00;
 204   2          //    TxCrcCalVal = crc16_x25(Uart.TxdData, 8);
 205   2          //    Uart.TxdData[8] = (unsigned char)(TxCrcCalVal >> 8);
 206   2          //    Uart.TxdData[9] = (unsigned char)(TxCrcCalVal & 0xFF);
 207   2        }
 208   1        else if (Ack == ACK) // Txd data Bit0: ACK/NCK
 209   1        {
 210   2          Uart.TxdData[0] = ACK; // Recive data CRC verify  normal  ---> Feedback ACK(0x06)
 211   2      
 212   2          Uart.TxdData[1] = 0xAA;
 213   2      
 214   2          switch (Uart.Cmd) // Txd data Bit1: feedback Inverter Status
 215   2          {
 216   3          case STANDBY:
 217   3          case STARTUP:
 218   3          case OPENLOOP:
 219   3          case ERROR:
 220   3            Uart.TxdData[2] = 0x00;
 221   3            break;
 222   3      
 223   3          case RURN:
 224   3            Uart.TxdData[2] = KeyProcStr.SpeedLevel;
 225   3            break;
 226   3      
 227   3          default:
 228   3            Uart.TxdData[2] = 0x00;
 229   3            break;
 230   3          }
 231   2      
 232   2          // 累加校验：对前3个字节求和并取低8位
 233   2          checksum = CalcChecksum(&Uart.TxdData[0], 3); //(Uart.TxdData[0] + Uart.TxdData[1] + Uart.TxdData[2]) % 
             -256;
 234   2          Uart.TxdData[3] = checksum;
 235   2          //    Uart.TxdData[4] = 0x00;
 236   2          //    Uart.TxdData[5] = 0x00;
 237   2          //    Uart.TxdData[6] = 0x00;
C51 COMPILER V9.59.0.0   COMMUNICATION                                                     06/24/2025 18:10:58 PAGE 5   

 238   2          //    Uart.TxdData[7] = 0x00;
 239   2          //    TxCrcCalVal = crc16_x25(Uart.TxdData, 8);
 240   2          //    Uart.TxdData[8] = (unsigned char)(TxCrcCalVal >> 8);
 241   2          //    Uart.TxdData[9] = (unsigned char)(TxCrcCalVal & 0xFF);
 242   2        }
 243   1        else if (Ack == NCK)
 244   1        {
 245   2          Uart.TxdData[0] = NCK; // Recive data CRC verify  abnormal  ---> Feedback NCK(0x15)
 246   2      
 247   2          Uart.TxdData[1] = 0xAA;
 248   2      
 249   2          switch (Uart.Cmd) // Txd data Bit1: feedback Inverter Status
 250   2          {
 251   3          case STANDBY:
 252   3          case STARTUP:
 253   3          case OPENLOOP:
 254   3          case ERROR:
 255   3            Uart.TxdData[2] = 0x00;
 256   3            break;
 257   3      
 258   3          case RURN:
 259   3            Uart.TxdData[2] = KeyProcStr.SpeedLevel;
 260   3            break;
 261   3      
 262   3          default:
 263   3            Uart.TxdData[2] = 0x00;
 264   3            break;
 265   3          }
 266   2      
 267   2          // 累加校验：对前3个字节求和并取低8位
 268   2          checksum = CalcChecksum(&Uart.TxdData[0], 3); //(Uart.TxdData[0] + Uart.TxdData[1] + Uart.TxdData[2]) % 
             -256;
 269   2          Uart.TxdData[3] = checksum;
 270   2          //    Uart.TxdData[4] = 0x00;
 271   2          //    Uart.TxdData[5] = 0x00;
 272   2          //    Uart.TxdData[6] = 0x00;
 273   2          //    Uart.TxdData[7] = 0x00;
 274   2          //    TxCrcCalVal = crc16_x25(Uart.TxdData, 8);
 275   2          //    Uart.TxdData[8] = (unsigned char)(TxCrcCalVal >> 8);
 276   2          //    Uart.TxdData[9] = (unsigned char)(TxCrcCalVal & 0xFF);
 277   2        }
 278   1      
 279   1        Uart.TxdDataCnt = 0;
 280   1        Uart.TxdDataLength = 3;
 281   1        REN = 0;        // 发送使能
 282   1        SBUF = Uart.TxdData[0]; // UART数据寄存器
 283   1      }
 284          
 285          /****************************************************************************
 286           * Function Description:CommDealResponse process
 287           * Input parameter     :void
 288           * Output paramter     :void
 289           ****************************************************************************/
 290          void CommDealResponse(void)
 291          {
 292   1        unsigned char rechecksum;
 293   1      
 294   1        if (Uart.RxdResponseFlag == 0x01)
 295   1        {
 296   2          rechecksum = CalcChecksum(&Uart.RxdData[0], 3);
 297   2      
 298   2          if (Uart.RxdData[3] == rechecksum)
C51 COMPILER V9.59.0.0   COMMUNICATION                                                     06/24/2025 18:10:58 PAGE 6   

 299   2          {
 300   3            // 命令处理
 301   3            switch (Uart.RxdData[2])
 302   3            {
 303   4            case 0x01:
 304   4              LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_1;
 305   4              KeyProcStr.SpeedLevel = 0x01; // 同时更新挡位变量
 306   4              break;
 307   4            case 0x02:
 308   4              LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_2;
 309   4              KeyProcStr.SpeedLevel = 0x02; 
 310   4              break;
 311   4            case 0x03:
 312   4              LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_3;
 313   4              KeyProcStr.SpeedLevel = 0x03; 
 314   4              break;
 315   4            case 0x04:
 316   4              LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_4;
 317   4              KeyProcStr.SpeedLevel = 0x04; 
 318   4              break;
 319   4            case 0x05:
 320   4              LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_5;
 321   4              KeyProcStr.SpeedLevel = 0x05; 
 322   4              break;
 323   4            case 0x06:
 324   4              LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_6;
 325   4              KeyProcStr.SpeedLevel = 0x06; 
 326   4              break;
 327   4            default:
 328   4              break;
 329   4            }
 330   3      
 331   3            // 成功，发送 ACK
 332   3            TxdDataProc(ACK);
 333   3          }
 334   2          else
 335   2          {
 336   3            // 校验失败，发送 NCK
 337   3            TxdDataProc(NCK);
 338   3          }
 339   2      
 340   2          // 重置标志
 341   2          Uart.RxdResponseFlag = 0;
 342   2          Uart.RxdCnt = 0;
 343   2          Uart.RxdFSM = 0;
 344   2        }
 345   1      }
 346          
 347          // void CommDealResponse(void)
 348          //{
 349          //     unsigned char xdata Ack;
 350          //
 351          //     if(Uart.TxdResponseFlag == 0x01)
 352          //     {
 353          //        Uart.TxdResponseFlag = 0x00;
 354          //        Ack = HEAD;                                         //定时发送数据，头码发0x68
 355          //
 356          //        Uart.RxdTimeOutCnt = 0x00;
 357          //        TxdDataProc(Ack);
 358          //    }
 359          //
 360          //
C51 COMPILER V9.59.0.0   COMMUNICATION                                                     06/24/2025 18:10:58 PAGE 7   

 361          ////    if(Uart.RxdResponseFlag == 0x01)
 362          ////    {
 363          ////        //Uart.Cmd = RURN;
 364          ////        //Uart.RxdData[2] = 30;
 365          ////        //Uart.RxdData[3] = 10;
 366          ////        //Uart.RxdData[4] = 10;
 367          ////        //Uart.RxdData[5] = 30;
 368          ////        //Uart.RxdData[6] = 10;
 369          ////        //Uart.RxdData[7] = 10;
 370          ////        //Uart.RxdData[8] = 0;
 371          ////        //Uart.RxdData[9] = 0xAA;
 372          ////        //Uart.RxdData[10] = 0xBB;
 373          
 374          ////        Uart.crcRecVal = (Uart.RxdData[9]<<8)|Uart.RxdData[10];               //11byte
 375          ////        Uart.crcCalVal = crc16_x25(Uart.RxdData,9);
 376          ////        if((Uart.crcRecVal == Uart.crcCalVal)||(Uart.crcRecVal == 0xAABB))    //测试模式0xAABB
 377          ////        {
 378          ////            Ack = ACK;                        //接收数据计算CRC同接收到CRC不相等，应答
 379          ////            Uart.RxdTimeOutCnt = 0x00;
 380          ////        }
 381          ////        else
 382          ////        {
 383          ////            Ack = NCK;                        //接收数据计算CRC同接收到CRC不相等，不应答
 384          ////            Uart.RxdCnt = 0x00;
 385          ////            Uart.RxdFSM = 0x00;
 386          ////            Uart.RxdResponseFlag = 0x00;
 387          ////        }
 388          ////
 389          ////        TxdDataProc(Ack);
 390          ////        Uart.RxdResponseFlag = 0x00;
 391          ////    }
 392          //}
 393          /****************************************************************************
 394           * Function Description:recived data txd test
 395           * Input parameter     :void
 396           * Output paramter     :void
 397           ****************************************************************************/
 398          // void UART_SendByte(unsigned char dat)
 399          // {
 400          //  unsigned int timeout = 5000; // 设定最大等待次数
 401          
 402          //  SBUF = dat;
 403          
 404          //  while (!TI && timeout--)
 405          //    ;
 406          //  TI = 0;
 407          //  REN = 1; // 发送完转接收状态
 408          // }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    394    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =     59       4
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
